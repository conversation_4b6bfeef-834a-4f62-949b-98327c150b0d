[{"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "DeleteAllGroupMembersByGroupId", "RelativePath": "api/Announcement/DeleteAllGroupMembersByGroupId", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "DeleteAnnouncement", "RelativePath": "api/Announcement/DeleteAnnouncement", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "DeleteGroup", "RelativePath": "api/Announcement/DeleteGroup", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementById", "RelativePath": "api/Announcement/GetAnnouncementById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementCategories", "RelativePath": "api/Announcement/GetAnnouncementCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementCategoriesByUserId", "RelativePath": "api/Announcement/GetAnnouncementCategoriesByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementCategoryById", "RelativePath": "api/Announcement/GetAnnouncementCategoryById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementDetailsById", "RelativePath": "api/Announcement/GetAnnouncementDetailsById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementReceivers", "RelativePath": "api/Announcement/GetAnnouncementReceivers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncements", "RelativePath": "api/Announcement/GetAnnouncements", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetAnnouncementsByUserId", "RelativePath": "api/Announcement/GetAnnouncementsByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetGroupById", "RelativePath": "api/Announcement/GetGroupById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetGroupMemberById", "RelativePath": "api/Announcement/GetGroupMemberById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetGroupMembers", "RelativePath": "api/Announcement/GetGroupMembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetGroupMembersByGroupId", "RelativePath": "api/Announcement/GetGroupMembersByGroupId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetGroups", "RelativePath": "api/Announcement/GetGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "GetUsersAnnouncementsByUserId", "RelativePath": "api/Announcement/GetUsersAnnouncementsByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "InsertAnnouncementReceiver", "RelativePath": "api/Announcement/InsertAnnouncementReceiver", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstAnnouncementReceiverDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "InsertGroupMembers", "RelativePath": "api/Announcement/InsertGroupMembers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstGroupMemberDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "InsertOrUpdateAnnouncement", "RelativePath": "api/Announcement/InsertOrUpdateAnnouncement", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.CreateAnnouncementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "InsertOrUpdateAnnouncementCategory", "RelativePath": "api/Announcement/InsertOrUpdateAnnouncementCategory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstAnnoucementCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "InsertOrUpdateGroup", "RelativePath": "api/Announcement/InsertOrUpdateGroup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstGroupDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "InsertOrUpdateGroupMember", "RelativePath": "api/Announcement/InsertOrUpdateGroupMember", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstGroupMemberDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "ToggleAnnouncementCategoryStatus", "RelativePath": "api/Announcement/ToggleAnnouncementCategoryStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "ToggleAnnouncementStatus", "RelativePath": "api/Announcement/ToggleAnnouncementStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "announcementId", "Type": "System.Int32", "IsRequired": false}, {"Name": "statusId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "UpdateAnnouncement", "RelativePath": "api/Announcement/UpdateAnnouncement", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.CreateAnnouncementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.AnnouncementController", "Method": "UpdateGroupMembers", "RelativePath": "api/Announcement/UpdateGroupMembers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstGroupMemberDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.DocumentLibraryController", "Method": "CreateOrUpdateDocument", "RelativePath": "api/DocumentLibrary/CreateOrUpdateDocument", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstDocumentLibraryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.DocumentLibraryController", "Method": "DeleteDocument", "RelativePath": "api/DocumentLibrary/DeleteDocument", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": false}, {"Name": "deletedBy", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.DocumentLibraryController", "Method": "GetDocumentById", "RelativePath": "api/DocumentLibrary/GetDocumentById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.DocumentLibraryController", "Method": "GetDocuments", "RelativePath": "api/DocumentLibrary/GetDocuments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.DocumentLibraryController", "Method": "GetDocumentsByUserId", "RelativePath": "api/DocumentLibrary/GetDocumentsByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "GetEventById", "RelativePath": "api/Event/GetEventById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "GetEventLikesByEventId", "RelativePath": "api/Event/GetEventLikesByEventId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "GetEventResponsesByEventId", "RelativePath": "api/Event/GetEventResponsesByEventId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "GetEventRsvpDetails", "RelativePath": "api/Event/GetEventRsvpDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "GetEvents", "RelativePath": "api/Event/GetEvents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "GetEventsByFacilityId", "RelativePath": "api/Event/GetEventsByFacilityId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityId", "Type": "System.Int32", "IsRequired": false}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}, {"Name": "dateFilterId", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "InsertOrUpdateEvent", "RelativePath": "api/Event/InsertOrUpdateEvent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstEventDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "SaveEventLike", "RelativePath": "api/Event/SaveEventLike", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": false}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "SaveEventResponse", "RelativePath": "api/Event/SaveEventResponse", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstEventResponseDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.EventController", "Method": "ToggleEventActivation", "RelativePath": "api/Event/ToggleEventActivation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "CreateOrUpdateActionParty", "RelativePath": "api/Inspection/CreateOrUpdateActionParty", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstActionPartyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "CreateOrUpdateInspection", "RelativePath": "api/Inspection/CreateOrUpdateInspection", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstInspectionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetActionPartyById", "RelativePath": "api/Inspection/GetActionPartyById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetActionPartyByUserId", "RelativePath": "api/Inspection/GetActionPartyByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetAllActionPartyByFacilityId", "RelativePath": "api/Inspection/GetAllActionPartyByFacilityId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetInspectionById", "RelativePath": "api/Inspection/GetInspectionById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inspectionId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetInspectionCategory", "RelativePath": "api/Inspection/GetInspectionCategory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetInspectionsByActionParty", "RelativePath": "api/Inspection/GetInspectionsByActionParty", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "actionPartyName", "Type": "System.Int32", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "GetUserInspections", "RelativePath": "api/Inspection/GetUserInspections", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "ToggleActionPartiesActivation", "RelativePath": "api/Inspection/ToggleActionPartiesActivation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "actionPartyId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.InspectionController", "Method": "UpdateInspectionItemStatus", "RelativePath": "api/Inspection/UpdateInspectionItemStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "HSSE_ModelDto.ModelDto.UpdateInspectionStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.LoginController", "Method": "GetUserByFacilityId", "RelativePath": "api/Login/GetUserByFacilityId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.LoginController", "Method": "GetUserFacility", "RelativePath": "api/Login/GetUserFacility", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.LoginController", "Method": "ValidateToken", "RelativePath": "api/Login/ValidateToken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "HSSE_ModelDto.ModelDto.LoginRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.NewsLetterController", "Method": "DeleteNewsLetter", "RelativePath": "api/NewsLetter/DeleteNewsLetter", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "newsLetterId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.NewsLetterController", "Method": "GetNewsLettersByFacilityId", "RelativePath": "api/NewsLetter/GetNewsLettersByFacilityId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityId", "Type": "System.Int32", "IsRequired": false}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.NewsLetterController", "Method": "GetNewsLettersById", "RelativePath": "api/NewsLetter/GetNewsLettersById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "newsletterId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.NewsLetterController", "Method": "GetNewsLettersByUserId", "RelativePath": "api/NewsLetter/GetNewsLettersByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.NewsLetterController", "Method": "InsertOrUpdateNewsLetter", "RelativePath": "api/NewsLetter/InsertOrUpdateNewsLetter", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstNewsletterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.NewsLetterController", "Method": "ToggleNewsLetterActivation", "RelativePath": "api/NewsLetter/ToggleNewsLetterActivation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "newsLetterId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PermissionController", "Method": "GetPermissionsByUserId", "RelativePath": "api/Permission/GetPermissionsByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "CreatePost", "RelativePath": "api/Posts/CreatePost", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "postDto", "Type": "HSSE_ModelDto.ModelDto.MstPostDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "DeletePost", "RelativePath": "api/Posts/DeletePost", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "postId", "Type": "System.Int32", "IsRequired": false}, {"Name": "deletedBy", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "DeletePostCategory", "RelativePath": "api/Posts/DeletePostCategory", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetAssignedPosts", "RelativePath": "api/Posts/GetAssignedPosts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetAssignedUser", "RelativePath": "api/Posts/GetAssignedUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "postId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetPostCategories", "RelativePath": "api/Posts/GetPostCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetPostCategoryById", "RelativePath": "api/Posts/GetPostCategoryById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetPostCommentByPostId", "RelativePath": "api/Posts/GetPostCommentByPostId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "postId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetPostDetails", "RelativePath": "api/Posts/GetPostDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetPostDetailsById", "RelativePath": "api/Posts/GetPostDetailsById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "GetPosts", "RelativePath": "api/Posts/GetPosts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "showOnlyMine", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "InsertOrUpdateFollowupPost", "RelativePath": "api/Posts/InsertOrUpdateFollowupPost", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstFollowupPostDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "InsertOrUpdatePostCategory", "RelativePath": "api/Posts/InsertOrUpdatePostCategory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstPostCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "InsertOrUpdatePostComment", "RelativePath": "api/Posts/InsertOrUpdatePostComment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.MstPostCommentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "ToggleLikeAsync", "RelativePath": "api/Posts/ToggleLike", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "postDto", "Type": "HSSE_ModelDto.ModelDto.MstLikesConfigDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HSSE_API.Controllers.PostsController", "Method": "UpdateFollowUpStatus", "RelativePath": "api/Posts/UpdateFollowUpStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "HSSE_ModelDto.ModelDto.FollowupStatusUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}]