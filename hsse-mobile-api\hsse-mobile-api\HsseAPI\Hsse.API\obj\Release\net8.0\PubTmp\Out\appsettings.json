{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs\\log-.log", "rollingInterval": "Day"}}]}, "ConnectionStrings": {"HsseDB": "Data Source=192.168.59.3;Initial Catalog=Hsse_17052025;Persist Security Info=True;User ID=sa;Password=**$q2023P@s; TrustServerCertificate=True"}, "AzureConnectionConfig": {"ImagePath": "https://uetrackstorage.blob.core.windows.net/Hsse/", "AccountName": "uemssgblob", "AccountKey": "****************************************************************************************", "ContainerName": "Hsse"}, "Url": {"MasterImagePath": "D:\\home\\site\\wwwroot\\MA\\MA_MasterWS\\Images\\Signature\\"}, "EmailSettings": {"FromEmail": "<EMAIL>", "SmtpHost": "smtp.sendgrid.net", "SmtpPort": "587", "EnableSSL": "true", "DefaultCredentials": "false", "SmtpUser": "apikey", "SmtpPass": "*********************************************************************"}, "JwtSettings": {"Key": "1E090CD7C2C437B0D3F3B8181674DACD54B840DE9A13584F574735FCA5B9ED58", "Issuer": "HsseAPI", "Audience": "HsseAPIUsers", "ExpiresInMinutes": 43200}, "AllowedHosts": "*"}