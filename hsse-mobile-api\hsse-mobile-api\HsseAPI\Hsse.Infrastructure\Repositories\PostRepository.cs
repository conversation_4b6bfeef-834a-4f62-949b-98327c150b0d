﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly MasterDBContext _context;
        private readonly ILogger<PostRepository> _logger;

        public PostRepository(MasterDBContext context, ILogger<PostRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<int> CreatePostAsync(CreatePostDto postDto)
        {
            try
            {
                var post = new MstPosts
                {
                    UserId = postDto.UserId,
                    FacilityId = postDto.FacilityId,
                    Title = postDto.Title,
                    Description = postDto.Description,
                    PostType = postDto.PostType,
                    Location = postDto.Location,
                    TaggedCategoryId = postDto.TaggedCategoryId,
                    RequiresFollowup = postDto.RequiresFollowup,
                    Status = postDto.Status,
                    MediaUrl = postDto.MediaUrl,
                    Priority = postDto.Priority,
                    IsActive = postDto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = postDto.UserId
                };

                _context.MstPosts.Add(post);
                await _context.SaveChangesAsync();

                // Handle assigned users if any
                if (postDto.AssignedTo?.Any() == true)
                {
                    await CreateFollowupAssignmentsAsync(post.PostId, postDto.AssignedTo, postDto.UserId, postDto.DueDate, postDto.Priority);
                }

                return post.PostId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating post");
                throw;
            }
        }

        public async Task<bool> UpdatePostAsync(UpdatePostDto postDto)
        {
            try
            {
                var post = await _context.MstPosts
                    .FirstOrDefaultAsync(p => p.PostId == postDto.PostId && p.IsActive == true);

                if (post == null)
                    return false;

                // Update properties
                if (!string.IsNullOrEmpty(postDto.Title))
                    post.Title = postDto.Title;

                if (!string.IsNullOrEmpty(postDto.Description))
                    post.Description = postDto.Description;

                if (!string.IsNullOrEmpty(postDto.Location))
                    post.Location = postDto.Location;

                if (postDto.TaggedCategoryId.HasValue)
                    post.TaggedCategoryId = postDto.TaggedCategoryId;

                post.RequiresFollowup = postDto.RequiresFollowup;

                if (postDto.Status.HasValue)
                    post.Status = postDto.Status;

                if (!string.IsNullOrEmpty(postDto.MediaUrl))
                    post.MediaUrl = postDto.MediaUrl;

                if (!string.IsNullOrEmpty(postDto.Priority))
                    post.Priority = postDto.Priority;

                post.ModifiedAt = DateTime.UtcNow;
                post.ModifiedBy = postDto.ModifiedBy;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating post with ID: {PostId}", postDto.PostId);
                throw;
            }
        }

        public async Task<bool> DeletePostAsync(int postId, int deletedBy)
        {
            try
            {
                var post = await _context.MstPosts
                    .FirstOrDefaultAsync(p => p.PostId == postId && p.IsActive == true);

                if (post == null)
                    return false;

                post.IsActive = false;
                post.ModifiedAt = DateTime.UtcNow;
                post.ModifiedBy = deletedBy;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting post with ID: {PostId}", postId);
                throw;
            }
        }

        public async Task<PostResponseDto?> GetPostByIdAsync(int postId, int? currentUserId = null)
        {
            try
            {
                var post = await _context.MstPosts
                    .Where(p => p.PostId == postId && p.IsActive == true)
                    .Select(p => new PostResponseDto
                    {
                        PostId = p.PostId,
                        UserId = p.UserId,
                        UserName = p.User != null ? p.User.UserName : null,
                        UserEmail = p.User != null ? p.User.Email : null,
                        FacilityId = p.FacilityId,
                        FacilityName = p.Facility != null ? p.Facility.FacilityName : null,
                        Title = p.Title,
                        Description = p.Description,
                        PostType = p.PostType,
                        Location = p.Location,
                        TaggedCategoryId = p.TaggedCategoryId,
                        CategoryName = p.Category != null ? p.Category.CategoryName : null,
                        RequiresFollowup = p.RequiresFollowup,
                        Status = p.Status,
                        StatusName = p.PostStatus != null ? p.PostStatus.StatusName : null,
                        MediaUrl = p.MediaUrl,
                        Priority = p.Priority,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        ModifiedAt = p.ModifiedAt,
                        LikeCount = _context.MstLikesConfig.Count(l => l.PostId == p.PostId && l.IsActive == true),
                        CommentCount = _context.MstPostComments.Count(c => c.PostId == p.PostId && c.IsActive == true),
                        IsLikedByCurrentUser = currentUserId.HasValue &&
                            _context.MstLikesConfig.Any(l => l.PostId == p.PostId && l.UserId == currentUserId && l.IsActive == true)
                    })
                    .FirstOrDefaultAsync();

                return post;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting post with ID: {PostId}", postId);
                throw;
            }
        }

        public async Task<List<PostResponseDto>> GetPostsAsync(int? userId = null, bool? showOnlyMine = null, int? facilityId = null, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.MstPosts
                    .Where(p => p.IsActive == true);

                // Apply filters
                if (showOnlyMine == true && userId.HasValue)
                {
                    query = query.Where(p => p.UserId == userId.Value);
                }

                if (facilityId.HasValue)
                {
                    query = query.Where(p => p.FacilityId == facilityId.Value);
                }

                var posts = await query
                    .OrderByDescending(p => p.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new PostResponseDto
                    {
                        PostId = p.PostId,
                        UserId = p.UserId,
                        UserName = p.User != null ? p.User.UserName : null,
                        UserEmail = p.User != null ? p.User.Email : null,
                        FacilityId = p.FacilityId,
                        FacilityName = p.Facility != null ? p.Facility.FacilityName : null,
                        Title = p.Title,
                        Description = p.Description,
                        PostType = p.PostType,
                        Location = p.Location,
                        TaggedCategoryId = p.TaggedCategoryId,
                        CategoryName = p.Category != null ? p.Category.CategoryName : null,
                        RequiresFollowup = p.RequiresFollowup,
                        Status = p.Status,
                        StatusName = p.PostStatus != null ? p.PostStatus.StatusName : null,
                        MediaUrl = p.MediaUrl,
                        Priority = p.Priority,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        ModifiedAt = p.ModifiedAt,
                        LikeCount = _context.MstLikesConfig.Count(l => l.PostId == p.PostId && l.IsActive == true),
                        CommentCount = _context.MstPostComments.Count(c => c.PostId == p.PostId && c.IsActive == true),
                        IsLikedByCurrentUser = userId.HasValue &&
                            _context.MstLikesConfig.Any(l => l.PostId == p.PostId && l.UserId == userId && l.IsActive == true)
                    })
                    .ToListAsync();

                return posts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting posts");
                throw;
            }
        }

        public async Task<List<PostResponseDto>> GetAssignedPostsAsync(int? userId, int? status = null)
        {
            try
            {
                var query = _context.MstPosts
                    .Where(p => p.IsActive == true)
                    .Where(p => _context.MstFollowupPosts.Any(fp => fp.PostId == p.PostId && fp.IsActive == true));

                // Apply filters
                if (userId.HasValue)
                {
                    query = query.Where(p => _context.MstFollowupPosts
                        .Any(fp => fp.PostId == p.PostId && fp.AssignedTo == userId.Value && fp.IsActive == true));
                }

                if (status.HasValue)
                {
                    query = query.Where(p => _context.MstFollowupPosts
                        .Any(fp => fp.PostId == p.PostId && fp.Status == status.ToString() && fp.IsActive == true));
                }

                var posts = await query
                    .OrderByDescending(p => p.CreatedAt)
                    .Select(p => new PostResponseDto
                    {
                        PostId = p.PostId,
                        UserId = p.UserId,
                        UserName = p.User != null ? p.User.UserName : null,
                        UserEmail = p.User != null ? p.User.Email : null,
                        FacilityId = p.FacilityId,
                        FacilityName = p.Facility != null ? p.Facility.FacilityName : null,
                        Title = p.Title,
                        Description = p.Description,
                        PostType = p.PostType,
                        Location = p.Location,
                        TaggedCategoryId = p.TaggedCategoryId,
                        CategoryName = p.Category != null ? p.Category.CategoryName : null,
                        RequiresFollowup = p.RequiresFollowup,
                        Status = p.Status,
                        StatusName = p.PostStatus != null ? p.PostStatus.StatusName : null,
                        MediaUrl = p.MediaUrl,
                        Priority = p.Priority,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        ModifiedAt = p.ModifiedAt,
                        LikeCount = _context.MstLikesConfig.Count(l => l.PostId == p.PostId && l.IsActive == true),
                        CommentCount = _context.MstPostComments.Count(c => c.PostId == p.PostId && c.IsActive == true),
                        IsLikedByCurrentUser = false // Set to false for assigned posts query
                    })
                    .ToListAsync();

                return posts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned posts");
                throw;
            }
        }

        public async Task<bool> ToggleLikeAsync(int postId, int userId)
        {
            try
            {
                // Check if like exists
                var existingLike = await _context.MstLikesConfig
                    .FirstOrDefaultAsync(l => l.PostId == postId && l.UserId == userId);

                if (existingLike != null)
                {
                    // Toggle existing like
                    existingLike.IsActive = !existingLike.IsActive;
                    existingLike.ModifiedAt = DateTime.UtcNow;
                }
                else
                {
                    // Create new like
                    var newLike = new MstLikesConfig
                    {
                        PostId = postId,
                        UserId = userId,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = userId
                    };
                    _context.MstLikesConfig.Add(newLike);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling like for post {PostId} by user {UserId}", postId, userId);
                throw;
            }
        }

        // Helper method for creating followup assignments
        private async Task CreateFollowupAssignmentsAsync(int postId, List<int> assignedTo, int assignedBy, DateTime? dueDate, string? priority)
        {
            foreach (var userId in assignedTo)
            {
                var followup = new MstFollowupPosts
                {
                    PostId = postId,
                    AssignedTo = userId,
                    AssignedBy = assignedBy,
                    DueDate = dueDate,
                    Priority = priority,
                    Status = "Pending",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = assignedBy
                };
                _context.MstFollowupPosts.Add(followup);
            }
            await _context.SaveChangesAsync();
        }

        // Remaining interface method implementations
        public async Task<int> GetLikeCountAsync(int postId)
        {
            return await _context.MstLikesConfig
                .CountAsync(l => l.PostId == postId && l.IsActive == true);
        }

        public async Task<bool> IsLikedByUserAsync(int postId, int userId)
        {
            return await _context.MstLikesConfig
                .AnyAsync(l => l.PostId == postId && l.UserId == userId && l.IsActive == true);
        }

        public async Task<int> CreateOrUpdatePostCategoryAsync(PostCategoryDto categoryDto)
        {
            if (categoryDto.CategoryId > 0)
            {
                // Update existing category
                var category = await _context.MstPostCategories
                    .FirstOrDefaultAsync(c => c.CategoryId == categoryDto.CategoryId);

                if (category != null)
                {
                    category.CategoryName = categoryDto.CategoryName;
                    category.Description = categoryDto.Description;
                    category.ModifiedAt = DateTime.UtcNow;
                    category.ModifiedBy = categoryDto.ModifiedBy;

                    await _context.SaveChangesAsync();
                }
                return categoryDto.CategoryId;
            }
            else
            {
                // Create new category
                var category = new MstPostCategories
                {
                    CategoryName = categoryDto.CategoryName,
                    Description = categoryDto.Description,
                    IsActive = categoryDto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = categoryDto.CreatedBy
                };

                _context.MstPostCategories.Add(category);
                await _context.SaveChangesAsync();
                return category.CategoryId;
            }
        }

        public async Task<bool> DeletePostCategoryAsync(int categoryId)
        {
            var category = await _context.MstPostCategories
                .FirstOrDefaultAsync(c => c.CategoryId == categoryId);

            if (category == null)
                return false;

            category.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<PostCategoryResponseDto?> GetPostCategoryByIdAsync(int categoryId)
        {
            return await _context.MstPostCategories
                .Where(pc => pc.CategoryId == categoryId)
                .Select(pc => new PostCategoryResponseDto
                {
                    CategoryId = pc.CategoryId,
                    CategoryName = pc.CategoryName,
                    Description = pc.Description,
                    IsActive = pc.IsActive,
                    CreatedAt = pc.CreatedAt,
                    ModifiedAt = pc.ModifiedAt,
                    CreatedBy = pc.CreatedBy,
                    CreatedByName = pc.CreatedByUser != null ? pc.CreatedByUser.UserName : null,
                    PostCount = _context.MstPosts.Count(p => p.TaggedCategoryId == categoryId && p.IsActive == true)
                })
                .FirstOrDefaultAsync();
        }

        public async Task<List<PostCategoryResponseDto>> GetPostCategoriesAsync()
        {
            return await _context.MstPostCategories
                .Where(pc => pc.IsActive == true)
                .OrderBy(pc => pc.CategoryName)
                .Select(pc => new PostCategoryResponseDto
                {
                    CategoryId = pc.CategoryId,
                    CategoryName = pc.CategoryName,
                    Description = pc.Description,
                    IsActive = pc.IsActive,
                    CreatedAt = pc.CreatedAt,
                    ModifiedAt = pc.ModifiedAt,
                    CreatedBy = pc.CreatedBy,
                    CreatedByName = pc.CreatedByUser != null ? pc.CreatedByUser.UserName : null,
                    PostCount = _context.MstPosts.Count(p => p.TaggedCategoryId == pc.CategoryId && p.IsActive == true)
                })
                .ToListAsync();
        }

        public async Task<int> CreateOrUpdatePostCommentAsync(PostCommentDto commentDto)
        {
            if (commentDto.CommentId > 0)
            {
                // Update existing comment
                var comment = await _context.MstPostComments
                    .FirstOrDefaultAsync(c => c.CommentId == commentDto.CommentId);

                if (comment != null)
                {
                    comment.Comment = commentDto.Comment;
                    comment.ModifiedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
                return commentDto.CommentId;
            }
            else
            {
                // Create new comment
                var comment = new MstPostComments
                {
                    PostId = commentDto.PostId,
                    UserId = commentDto.UserId,
                    Comment = commentDto.Comment,
                    ParentCommentId = commentDto.ParentCommentId,
                    IsActive = commentDto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = commentDto.UserId
                };

                _context.MstPostComments.Add(comment);
                await _context.SaveChangesAsync();
                return comment.CommentId;
            }
        }

        public async Task<List<PostCommentResponseDto>> GetPostCommentsAsync(int postId)
        {
            return await _context.MstPostComments
                .Where(c => c.PostId == postId && c.IsActive == true)
                .OrderBy(c => c.CreatedAt)
                .Select(c => new PostCommentResponseDto
                {
                    CommentId = c.CommentId,
                    PostId = c.PostId,
                    UserId = c.UserId,
                    UserName = c.User != null ? c.User.UserName : null,
                    Comment = c.Comment,
                    ParentCommentId = c.ParentCommentId,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt
                })
                .ToListAsync();
        }

        public async Task<int> CreateOrUpdateFollowupPostAsync(FollowupPostDto followupDto)
        {
            if (followupDto.FollowupId > 0)
            {
                // Update existing followup
                var followup = await _context.MstFollowupPosts
                    .FirstOrDefaultAsync(f => f.FollowupId == followupDto.FollowupId);

                if (followup != null)
                {
                    followup.DueDate = followupDto.DueDate;
                    followup.Priority = followupDto.Priority;
                    followup.Status = followupDto.Status;
                    followup.Comments = followupDto.Comments;
                    followup.ModifiedAt = DateTime.UtcNow;

                    await _context.SaveChangesAsync();
                }
                return followupDto.FollowupId;
            }
            else
            {
                // Create new followup
                var followup = new MstFollowupPosts
                {
                    PostId = followupDto.PostId,
                    AssignedTo = followupDto.AssignedTo,
                    AssignedBy = followupDto.AssignedBy,
                    DueDate = followupDto.DueDate,
                    Priority = followupDto.Priority,
                    Status = followupDto.Status,
                    Comments = followupDto.Comments,
                    IsActive = followupDto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = followupDto.AssignedBy
                };

                _context.MstFollowupPosts.Add(followup);
                await _context.SaveChangesAsync();
                return followup.FollowupId;
            }
        }

        public async Task<bool> UpdateFollowupStatusAsync(FollowupStatusUpdateDto statusUpdateDto)
        {
            var followup = await _context.MstFollowupPosts
                .FirstOrDefaultAsync(f => f.FollowupId == statusUpdateDto.FollowupId);

            if (followup == null)
                return false;

            followup.Status = statusUpdateDto.Status;
            followup.Comments = statusUpdateDto.Comments;

            if (statusUpdateDto.Status == "Completed")
                followup.CompletedAt = DateTime.UtcNow;

            followup.ModifiedAt = DateTime.UtcNow;
            followup.ModifiedBy = statusUpdateDto.UpdatedBy;

            // Handle media URLs if provided
            if (statusUpdateDto.AfterMediaUrl?.Any() == true)
            {
                var mediaUrls = string.Join(",", statusUpdateDto.AfterMediaUrl);
                followup.AfterMediaUrl = mediaUrls;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<FollowupPostResponseDto>> GetFollowupsByPostIdAsync(int postId)
        {
            var followups = await _context.MstFollowupPosts
                .Where(fp => fp.PostId == postId && fp.IsActive == true)
                .OrderByDescending(fp => fp.CreatedAt)
                .Select(fp => new FollowupPostResponseDto
                {
                    FollowupId = fp.FollowupId,
                    PostId = fp.PostId,
                    AssignedTo = fp.AssignedTo,
                    AssignedToName = fp.AssignedToUser != null ? fp.AssignedToUser.UserName : null,
                    AssignedBy = fp.AssignedBy,
                    AssignedByName = fp.AssignedByUser != null ? fp.AssignedByUser.UserName : null,
                    DueDate = fp.DueDate,
                    Priority = fp.Priority,
                    Status = fp.Status,
                    Comments = fp.Comments,
                    BeforeMediaUrl = !string.IsNullOrEmpty(fp.BeforeMediaUrl) ? fp.BeforeMediaUrl.Split(',').ToList() : null,
                    AfterMediaUrl = !string.IsNullOrEmpty(fp.AfterMediaUrl) ? fp.AfterMediaUrl.Split(',').ToList() : null,
                    CompletedAt = fp.CompletedAt,
                    CreatedAt = fp.CreatedAt,
                    IsActive = fp.IsActive
                })
                .ToListAsync();

            return followups;
        }

        public async Task<List<AssignedUserResponseDto>> GetAssignedUsersAsync(int postId)
        {
            return await _context.MstFollowupPosts
                .Where(fp => fp.PostId == postId && fp.IsActive == true)
                .Select(fp => fp.AssignedToUser)
                .Distinct()
                .Select(u => new AssignedUserResponseDto
                {
                    UserId = u.UserId,
                    UserName = u.UserName,
                    Email = u.Email,
                    Role = u.Role,
                    FacilityId = u.FacilityId,
                    FacilityName = u.Facility != null ? u.Facility.FacilityName : null
                })
                .ToListAsync();
        }

        public async Task<int> GetTotalPostCountAsync(int? facilityId = null)
        {
            var query = _context.MstPosts.Where(p => p.IsActive == true);

            if (facilityId.HasValue)
            {
                query = query.Where(p => p.FacilityId == facilityId.Value);
            }

            return await query.CountAsync();
        }

        public async Task<int> GetActivePostCountAsync(int? facilityId = null)
        {
            var query = _context.MstPosts.Where(p => p.IsActive == true && p.Status != "Closed");

            if (facilityId.HasValue)
            {
                query = query.Where(p => p.FacilityId == facilityId.Value);
            }

            return await query.CountAsync();
        }

        public async Task<int> GetPendingFollowupCountAsync(int? userId = null)
        {
            var query = _context.MstFollowupPosts.Where(fp => fp.IsActive == true && fp.Status == "Pending");

            if (userId.HasValue)
            {
                query = query.Where(fp => fp.AssignedTo == userId.Value);
            }

            return await query.CountAsync();
        }
    }
}
