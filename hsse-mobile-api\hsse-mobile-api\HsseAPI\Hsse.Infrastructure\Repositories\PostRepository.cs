﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        public PostRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }



    }
}
