using Dapper;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<PostRepository> _logger;
        private readonly string _connectionString;

        public PostRepository(IConfiguration configuration, ILogger<PostRepository> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _connectionString = _configuration.GetConnectionString("HsseDB") ?? throw new ArgumentNullException("Connection string not found");
        }

        public async Task<int> CreatePostAsync(CreatePostDto postDto)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    INSERT INTO MstPosts (UserId, FacilityId, Title, Description, PostType, Location,
                                        TaggedCategoryId, RequiresFollowup, Status, MediaUrl,
                                        Priority, IsActive, CreatedAt, CreatedBy)
                    OUTPUT INSERTED.PostId
                    VALUES (@UserId, @FacilityId, @Title, @Description, @PostType, @Location,
                           @TaggedCategoryId, @RequiresFollowup, @Status, @MediaUrl,
                           @Priority, @IsActive, GETUTCDATE(), @UserId)";

                var parameters = new
                {
                    postDto.UserId,
                    postDto.FacilityId,
                    postDto.Title,
                    postDto.Description,
                    postDto.PostType,
                    postDto.Location,
                    postDto.TaggedCategoryId,
                    postDto.RequiresFollowup,
                    postDto.Status,
                    postDto.MediaUrl,
                    postDto.Priority,
                    postDto.IsActive
                };

                var postId = await connection.QuerySingleAsync<int>(sql, parameters);

                // Handle assigned users if any
                if (postDto.AssignedTo?.Any() == true)
                {
                    await CreateFollowupAssignmentsAsync(connection, postId, postDto.AssignedTo, postDto.UserId, postDto.DueDate, postDto.Priority);
                }

                return postId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating post");
                throw;
            }
        }

        public async Task<bool> UpdatePostAsync(UpdatePostDto postDto)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    UPDATE MstPosts
                    SET Title = @Title, Description = @Description, Location = @Location,
                        TaggedCategoryId = @TaggedCategoryId, RequiresFollowup = @RequiresFollowup,
                        Status = @Status, MediaUrl = @MediaUrl, Priority = @Priority,
                        ModifiedAt = GETUTCDATE(), ModifiedBy = @ModifiedBy
                    WHERE PostId = @PostId";

                var parameters = new
                {
                    postDto.PostId,
                    postDto.Title,
                    postDto.Description,
                    postDto.Location,
                    postDto.TaggedCategoryId,
                    postDto.RequiresFollowup,
                    postDto.Status,
                    postDto.MediaUrl,
                    postDto.Priority,
                    postDto.ModifiedBy
                };

                var rowsAffected = await connection.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating post with ID: {PostId}", postDto.PostId);
                throw;
            }
        }

        public async Task<bool> DeletePostAsync(int postId, int deletedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    UPDATE MstPosts
                    SET IsActive = 0, ModifiedAt = GETUTCDATE(), ModifiedBy = @DeletedBy
                    WHERE PostId = @PostId";

                var rowsAffected = await connection.ExecuteAsync(sql, new { PostId = postId, DeletedBy = deletedBy });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting post with ID: {PostId}", postId);
                throw;
            }
        }

        public async Task<PostResponseDto?> GetPostByIdAsync(int postId, int? currentUserId = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    SELECT p.PostId, p.UserId, u.UserName, u.Email as UserEmail, p.FacilityId, f.FacilityName,
                           p.Title, p.Description, p.PostType, p.Location, p.TaggedCategoryId, pc.CategoryName,
                           p.RequiresFollowup, p.Status, ps.StatusName, p.MediaUrl, p.Priority, p.IsActive,
                           p.CreatedAt, p.ModifiedAt,
                           (SELECT COUNT(*) FROM MstLikesConfig WHERE PostId = p.PostId AND IsActive = 1) as LikeCount,
                           (SELECT COUNT(*) FROM MstPostComments WHERE PostId = p.PostId AND IsActive = 1) as CommentCount,
                           CASE WHEN @CurrentUserId IS NOT NULL AND EXISTS(SELECT 1 FROM MstLikesConfig WHERE PostId = p.PostId AND UserId = @CurrentUserId AND IsActive = 1)
                                THEN 1 ELSE 0 END as IsLikedByCurrentUser
                    FROM MstPosts p
                    LEFT JOIN MstUsers u ON p.UserId = u.UserId
                    LEFT JOIN MstFacilities f ON p.FacilityId = f.FacilityId
                    LEFT JOIN MstPostCategories pc ON p.TaggedCategoryId = pc.CategoryId
                    LEFT JOIN MstPostStatus ps ON p.Status = ps.StatusId
                    WHERE p.PostId = @PostId AND p.IsActive = 1";

                var post = await connection.QuerySingleOrDefaultAsync<PostResponseDto>(sql, new { PostId = postId, CurrentUserId = currentUserId });
                return post;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting post with ID: {PostId}", postId);
                throw;
            }
        }

        public async Task<List<PostResponseDto>> GetPostsAsync(int? userId = null, bool? showOnlyMine = null, int? facilityId = null, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var whereConditions = new List<string> { "p.IsActive = 1" };
                var parameters = new DynamicParameters();

                if (showOnlyMine == true && userId.HasValue)
                {
                    whereConditions.Add("p.UserId = @UserId");
                    parameters.Add("UserId", userId.Value);
                }

                if (facilityId.HasValue)
                {
                    whereConditions.Add("p.FacilityId = @FacilityId");
                    parameters.Add("FacilityId", facilityId.Value);
                }

                parameters.Add("Offset", (pageNumber - 1) * pageSize);
                parameters.Add("PageSize", pageSize);
                parameters.Add("CurrentUserId", userId);

                var sql = $@"
                    SELECT p.PostId, p.UserId, u.UserName, u.Email as UserEmail, p.FacilityId, f.FacilityName,
                           p.Title, p.Description, p.PostType, p.Location, p.TaggedCategoryId, pc.CategoryName,
                           p.RequiresFollowup, p.Status, ps.StatusName, p.MediaUrl, p.Priority, p.IsActive,
                           p.CreatedAt, p.ModifiedAt,
                           (SELECT COUNT(*) FROM MstLikesConfig WHERE PostId = p.PostId AND IsActive = 1) as LikeCount,
                           (SELECT COUNT(*) FROM MstPostComments WHERE PostId = p.PostId AND IsActive = 1) as CommentCount,
                           CASE WHEN @CurrentUserId IS NOT NULL AND EXISTS(SELECT 1 FROM MstLikesConfig WHERE PostId = p.PostId AND UserId = @CurrentUserId AND IsActive = 1)
                                THEN 1 ELSE 0 END as IsLikedByCurrentUser
                    FROM MstPosts p
                    LEFT JOIN MstUsers u ON p.UserId = u.UserId
                    LEFT JOIN MstFacilities f ON p.FacilityId = f.FacilityId
                    LEFT JOIN MstPostCategories pc ON p.TaggedCategoryId = pc.CategoryId
                    LEFT JOIN MstPostStatus ps ON p.Status = ps.StatusId
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY p.CreatedAt DESC
                    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                var posts = await connection.QueryAsync<PostResponseDto>(sql, parameters);
                return posts.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting posts");
                throw;
            }
        }

        public async Task<List<PostResponseDto>> GetAssignedPostsAsync(int? userId, int? status = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var whereConditions = new List<string> { "p.IsActive = 1", "fp.IsActive = 1" };
                var parameters = new DynamicParameters();

                if (userId.HasValue)
                {
                    whereConditions.Add("fp.AssignedTo = @UserId");
                    parameters.Add("UserId", userId.Value);
                }

                if (status.HasValue)
                {
                    whereConditions.Add("fp.Status = @Status");
                    parameters.Add("Status", status.Value);
                }

                var sql = $@"
                    SELECT DISTINCT p.PostId, p.UserId, u.UserName, u.Email as UserEmail, p.FacilityId, f.FacilityName,
                           p.Title, p.Description, p.PostType, p.Location, p.TaggedCategoryId, pc.CategoryName,
                           p.RequiresFollowup, p.Status, ps.StatusName, p.MediaUrl, p.Priority, p.IsActive,
                           p.CreatedAt, p.ModifiedAt,
                           (SELECT COUNT(*) FROM MstLikesConfig WHERE PostId = p.PostId AND IsActive = 1) as LikeCount,
                           (SELECT COUNT(*) FROM MstPostComments WHERE PostId = p.PostId AND IsActive = 1) as CommentCount,
                           0 as IsLikedByCurrentUser
                    FROM MstPosts p
                    INNER JOIN MstFollowupPosts fp ON p.PostId = fp.PostId
                    LEFT JOIN MstUsers u ON p.UserId = u.UserId
                    LEFT JOIN MstFacilities f ON p.FacilityId = f.FacilityId
                    LEFT JOIN MstPostCategories pc ON p.TaggedCategoryId = pc.CategoryId
                    LEFT JOIN MstPostStatus ps ON p.Status = ps.StatusId
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY p.CreatedAt DESC";

                var posts = await connection.QueryAsync<PostResponseDto>(sql, parameters);
                return posts.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned posts");
                throw;
            }
        }

        public async Task<bool> ToggleLikeAsync(int postId, int userId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);

                // Check if like exists
                var existingLike = await connection.QuerySingleOrDefaultAsync<int?>(
                    "SELECT LikeId FROM MstLikesConfig WHERE PostId = @PostId AND UserId = @UserId",
                    new { PostId = postId, UserId = userId });

                if (existingLike.HasValue)
                {
                    // Toggle existing like
                    var sql = @"
                        UPDATE MstLikesConfig
                        SET IsActive = CASE WHEN IsActive = 1 THEN 0 ELSE 1 END,
                            ModifiedAt = GETUTCDATE()
                        WHERE PostId = @PostId AND UserId = @UserId";

                    await connection.ExecuteAsync(sql, new { PostId = postId, UserId = userId });
                }
                else
                {
                    // Create new like
                    var sql = @"
                        INSERT INTO MstLikesConfig (PostId, UserId, IsActive, CreatedAt, CreatedBy)
                        VALUES (@PostId, @UserId, 1, GETUTCDATE(), @UserId)";

                    await connection.ExecuteAsync(sql, new { PostId = postId, UserId = userId });
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling like for post {PostId} by user {UserId}", postId, userId);
                throw;
            }
        }

        // Helper method for creating followup assignments
        private async Task CreateFollowupAssignmentsAsync(IDbConnection connection, int postId, List<int> assignedTo, int assignedBy, DateTime? dueDate, string? priority)
        {
            foreach (var userId in assignedTo)
            {
                var sql = @"
                    INSERT INTO MstFollowupPosts (PostId, AssignedTo, AssignedBy, DueDate, Priority, Status, IsActive, CreatedAt, CreatedBy)
                    VALUES (@PostId, @AssignedTo, @AssignedBy, @DueDate, @Priority, 'Pending', 1, GETUTCDATE(), @AssignedBy)";

                await connection.ExecuteAsync(sql, new
                {
                    PostId = postId,
                    AssignedTo = userId,
                    AssignedBy = assignedBy,
                    DueDate = dueDate,
                    Priority = priority
                });
            }
        }

        // Placeholder implementations for remaining interface methods
        public async Task<int> GetLikeCountAsync(int postId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT COUNT(*) FROM MstLikesConfig WHERE PostId = @PostId AND IsActive = 1";
            return await connection.QuerySingleAsync<int>(sql, new { PostId = postId });
        }

        public async Task<bool> IsLikedByUserAsync(int postId, int userId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT COUNT(*) FROM MstLikesConfig WHERE PostId = @PostId AND UserId = @UserId AND IsActive = 1";
            var count = await connection.QuerySingleAsync<int>(sql, new { PostId = postId, UserId = userId });
            return count > 0;
        }

        public async Task<int> CreateOrUpdatePostCategoryAsync(PostCategoryDto categoryDto)
        {
            using var connection = new SqlConnection(_connectionString);

            if (categoryDto.CategoryId > 0)
            {
                var updateSql = @"
                    UPDATE MstPostCategories
                    SET CategoryName = @CategoryName, Description = @Description,
                        ModifiedAt = GETUTCDATE(), ModifiedBy = @ModifiedBy
                    WHERE CategoryId = @CategoryId";

                await connection.ExecuteAsync(updateSql, categoryDto);
                return categoryDto.CategoryId;
            }
            else
            {
                var insertSql = @"
                    INSERT INTO MstPostCategories (CategoryName, Description, IsActive, CreatedAt, CreatedBy)
                    OUTPUT INSERTED.CategoryId
                    VALUES (@CategoryName, @Description, @IsActive, GETUTCDATE(), @CreatedBy)";

                return await connection.QuerySingleAsync<int>(insertSql, categoryDto);
            }
        }

        public async Task<bool> DeletePostCategoryAsync(int categoryId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "UPDATE MstPostCategories SET IsActive = 0 WHERE CategoryId = @CategoryId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { CategoryId = categoryId });
            return rowsAffected > 0;
        }

        public async Task<PostCategoryResponseDto?> GetPostCategoryByIdAsync(int categoryId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                SELECT CategoryId, CategoryName, Description, IsActive, CreatedAt, ModifiedAt,
                       CreatedBy, u.UserName as CreatedByName,
                       (SELECT COUNT(*) FROM MstPosts WHERE TaggedCategoryId = @CategoryId AND IsActive = 1) as PostCount
                FROM MstPostCategories pc
                LEFT JOIN MstUsers u ON pc.CreatedBy = u.UserId
                WHERE pc.CategoryId = @CategoryId";

            return await connection.QuerySingleOrDefaultAsync<PostCategoryResponseDto>(sql, new { CategoryId = categoryId });
        }

        public async Task<List<PostCategoryResponseDto>> GetPostCategoriesAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                SELECT CategoryId, CategoryName, Description, IsActive, CreatedAt, ModifiedAt,
                       CreatedBy, u.UserName as CreatedByName,
                       (SELECT COUNT(*) FROM MstPosts WHERE TaggedCategoryId = pc.CategoryId AND IsActive = 1) as PostCount
                FROM MstPostCategories pc
                LEFT JOIN MstUsers u ON pc.CreatedBy = u.UserId
                WHERE pc.IsActive = 1
                ORDER BY pc.CategoryName";

            var categories = await connection.QueryAsync<PostCategoryResponseDto>(sql);
            return categories.ToList();
        }

        public async Task<int> CreateOrUpdatePostCommentAsync(PostCommentDto commentDto)
        {
            using var connection = new SqlConnection(_connectionString);

            if (commentDto.CommentId > 0)
            {
                var updateSql = @"
                    UPDATE MstPostComments
                    SET Comment = @Comment, ModifiedAt = GETUTCDATE()
                    WHERE CommentId = @CommentId";

                await connection.ExecuteAsync(updateSql, commentDto);
                return commentDto.CommentId;
            }
            else
            {
                var insertSql = @"
                    INSERT INTO MstPostComments (PostId, UserId, Comment, ParentCommentId, IsActive, CreatedAt, CreatedBy)
                    OUTPUT INSERTED.CommentId
                    VALUES (@PostId, @UserId, @Comment, @ParentCommentId, @IsActive, GETUTCDATE(), @UserId)";

                return await connection.QuerySingleAsync<int>(insertSql, commentDto);
            }
        }

        public async Task<List<PostCommentResponseDto>> GetPostCommentsAsync(int postId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                SELECT c.CommentId, c.PostId, c.UserId, u.UserName, c.Comment,
                       c.ParentCommentId, c.IsActive, c.CreatedAt
                FROM MstPostComments c
                LEFT JOIN MstUsers u ON c.UserId = u.UserId
                WHERE c.PostId = @PostId AND c.IsActive = 1
                ORDER BY c.CreatedAt ASC";

            var comments = await connection.QueryAsync<PostCommentResponseDto>(sql, new { PostId = postId });
            return comments.ToList();
        }

        public async Task<int> CreateOrUpdateFollowupPostAsync(FollowupPostDto followupDto)
        {
            using var connection = new SqlConnection(_connectionString);

            if (followupDto.FollowupId > 0)
            {
                var updateSql = @"
                    UPDATE MstFollowupPosts
                    SET DueDate = @DueDate, Priority = @Priority, Status = @Status,
                        Comments = @Comments, ModifiedAt = GETUTCDATE()
                    WHERE FollowupId = @FollowupId";

                await connection.ExecuteAsync(updateSql, followupDto);
                return followupDto.FollowupId;
            }
            else
            {
                var insertSql = @"
                    INSERT INTO MstFollowupPosts (PostId, AssignedTo, AssignedBy, DueDate, Priority, Status,
                                                Comments, IsActive, CreatedAt, CreatedBy)
                    OUTPUT INSERTED.FollowupId
                    VALUES (@PostId, @AssignedTo, @AssignedBy, @DueDate, @Priority, @Status,
                           @Comments, @IsActive, GETUTCDATE(), @AssignedBy)";

                return await connection.QuerySingleAsync<int>(insertSql, followupDto);
            }
        }

        public async Task<bool> UpdateFollowupStatusAsync(FollowupStatusUpdateDto statusUpdateDto)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                UPDATE MstFollowupPosts
                SET Status = @Status, Comments = @Comments,
                    CompletedAt = CASE WHEN @Status = 'Completed' THEN GETUTCDATE() ELSE CompletedAt END,
                    ModifiedAt = GETUTCDATE(), ModifiedBy = @UpdatedBy
                WHERE FollowupId = @FollowupId";

            // Handle media URLs if provided
            if (statusUpdateDto.AfterMediaUrl?.Any() == true)
            {
                var mediaUrls = string.Join(",", statusUpdateDto.AfterMediaUrl);
                sql = @"
                    UPDATE MstFollowupPosts
                    SET Status = @Status, Comments = @Comments, AfterMediaUrl = @AfterMediaUrl,
                        CompletedAt = CASE WHEN @Status = 'Completed' THEN GETUTCDATE() ELSE CompletedAt END,
                        ModifiedAt = GETUTCDATE(), ModifiedBy = @UpdatedBy
                    WHERE FollowupId = @FollowupId";

                var rowsAffected = await connection.ExecuteAsync(sql, new
                {
                    statusUpdateDto.FollowupId,
                    statusUpdateDto.Status,
                    statusUpdateDto.Comments,
                    AfterMediaUrl = mediaUrls,
                    statusUpdateDto.UpdatedBy
                });
                return rowsAffected > 0;
            }
            else
            {
                var rowsAffected = await connection.ExecuteAsync(sql, statusUpdateDto);
                return rowsAffected > 0;
            }
        }

        public async Task<List<FollowupPostResponseDto>> GetFollowupsByPostIdAsync(int postId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                SELECT fp.FollowupId, fp.PostId, fp.AssignedTo, u1.UserName as AssignedToName,
                       fp.AssignedBy, u2.UserName as AssignedByName, fp.DueDate, fp.Priority,
                       fp.Status, fp.Comments, fp.BeforeMediaUrl, fp.AfterMediaUrl,
                       fp.CompletedAt, fp.CreatedAt, fp.IsActive
                FROM MstFollowupPosts fp
                LEFT JOIN MstUsers u1 ON fp.AssignedTo = u1.UserId
                LEFT JOIN MstUsers u2 ON fp.AssignedBy = u2.UserId
                WHERE fp.PostId = @PostId AND fp.IsActive = 1
                ORDER BY fp.CreatedAt DESC";

            var followups = await connection.QueryAsync<FollowupPostResponseDto>(sql, new { PostId = postId });

            // Parse comma-separated media URLs back to lists
            foreach (var followup in followups)
            {
                if (!string.IsNullOrEmpty(followup.BeforeMediaUrl?.FirstOrDefault()))
                {
                    followup.BeforeMediaUrl = followup.BeforeMediaUrl.FirstOrDefault()?.Split(',').ToList();
                }
                if (!string.IsNullOrEmpty(followup.AfterMediaUrl?.FirstOrDefault()))
                {
                    followup.AfterMediaUrl = followup.AfterMediaUrl.FirstOrDefault()?.Split(',').ToList();
                }
            }

            return followups.ToList();
        }

        public async Task<List<AssignedUserResponseDto>> GetAssignedUsersAsync(int postId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                SELECT DISTINCT u.UserId, u.UserName, u.Email, u.Role, u.FacilityId, f.FacilityName
                FROM MstFollowupPosts fp
                INNER JOIN MstUsers u ON fp.AssignedTo = u.UserId
                LEFT JOIN MstFacilities f ON u.FacilityId = f.FacilityId
                WHERE fp.PostId = @PostId AND fp.IsActive = 1";

            var users = await connection.QueryAsync<AssignedUserResponseDto>(sql, new { PostId = postId });
            return users.ToList();
        }

        public async Task<int> GetTotalPostCountAsync(int? facilityId = null)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT COUNT(*) FROM MstPosts WHERE IsActive = 1";
            var parameters = new DynamicParameters();

            if (facilityId.HasValue)
            {
                sql += " AND FacilityId = @FacilityId";
                parameters.Add("FacilityId", facilityId.Value);
            }

            return await connection.QuerySingleAsync<int>(sql, parameters);
        }

        public async Task<int> GetActivePostCountAsync(int? facilityId = null)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT COUNT(*) FROM MstPosts WHERE IsActive = 1 AND Status != 'Closed'";
            var parameters = new DynamicParameters();

            if (facilityId.HasValue)
            {
                sql += " AND FacilityId = @FacilityId";
                parameters.Add("FacilityId", facilityId.Value);
            }

            return await connection.QuerySingleAsync<int>(sql, parameters);
        }

        public async Task<int> GetPendingFollowupCountAsync(int? userId = null)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT COUNT(*) FROM MstFollowupPosts WHERE IsActive = 1 AND Status = 'Pending'";
            var parameters = new DynamicParameters();

            if (userId.HasValue)
            {
                sql += " AND AssignedTo = @UserId";
                parameters.Add("UserId", userId.Value);
            }

            return await connection.QuerySingleAsync<int>(sql, parameters);
        }
    }
}
