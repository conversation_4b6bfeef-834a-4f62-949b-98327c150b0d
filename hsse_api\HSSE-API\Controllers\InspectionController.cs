using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class InspectionController : ControllerBase
    {
        private readonly IInspectionService _service;

        public InspectionController(IInspectionService service)
        {
            _service = service;
        }

        [HttpPost]
        public async Task<IActionResult> CreateOrUpdateActionParty([FromBody] MstActionPartyDto dto)
        {
            var result = await _service.CreateOrUpdateActionPartyAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetActionPartyById(int id)
        {
            var result = await _service.GetActionPartyByIdAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllActionPartyByFacilityId(int facilityId)
        {
            var result = await _service.GetActionPartiesByFacilityIdAsync(facilityId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetActionPartyByUserId(int userId)
        {
            var result = await _service.GetActionPartiesByUserIdAsync(userId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> ToggleActionPartiesActivation(int actionPartyId)
        {
            try
            {
                var result = await _service.ToggleActionPartiesActivationAsync(actionPartyId);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateOrUpdateInspection([FromBody] MstInspectionDto dto)
        {
            string externalRootPath = @"C:\HSSE-Announcements";
            string uploadFolder = Path.Combine(externalRootPath, "inspection-files");

            if (!Directory.Exists(uploadFolder))
                Directory.CreateDirectory(uploadFolder);

            // Save Observation Attachment
            if (!string.IsNullOrEmpty(dto.ObservationAttachmentBase64) && !string.IsNullOrEmpty(dto.ObservationAttachmentName))
            {
                string uniqueFileName = $"{Guid.NewGuid()}_{dto.ObservationAttachmentName}";
                string filePath = Path.Combine(uploadFolder, uniqueFileName);

                string base64Data = dto.ObservationAttachmentBase64.Contains(",")
                    ? dto.ObservationAttachmentBase64.Substring(dto.ObservationAttachmentBase64.IndexOf(",") + 1)
                    : dto.ObservationAttachmentBase64;

                byte[] fileBytes = Convert.FromBase64String(base64Data);
                await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                dto.ObservationAttachmentBase64 = $"/ExternalFiles/inspection-files/{uniqueFileName}";
            }

            // Save Recommendation Attachment
            if (!string.IsNullOrEmpty(dto.RecommendationAttachmentBase64) && !string.IsNullOrEmpty(dto.RecommendationAttachmentName))
            {
                string uniqueFileName = $"{Guid.NewGuid()}_{dto.RecommendationAttachmentName}";
                string filePath = Path.Combine(uploadFolder, uniqueFileName);

                string base64Data = dto.RecommendationAttachmentBase64.Contains(",")
                    ? dto.RecommendationAttachmentBase64.Substring(dto.RecommendationAttachmentBase64.IndexOf(",") + 1)
                    : dto.RecommendationAttachmentBase64;

                byte[] fileBytes = Convert.FromBase64String(base64Data);
                await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                dto.RecommendationAttachmentBase64 = $"/ExternalFiles/inspection-files/{uniqueFileName}";
            }
            var result = await _service.CreateOrUpdateInspectionAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetInspectionById(int inspectionId)
        {
            var result = await _service.GetInspectionByIdAsync(inspectionId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetInspectionsByActionParty(int actionPartyName, int? status)
        {
            var result = await _service.GetInspectionsByActionPartyAsync(actionPartyName, status);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateInspectionItemStatus(UpdateInspectionStatusDto req)
        {
            try
            {

                string externalRootPath = @"C:\HSSE-Announcements";
                string uploadFolder = Path.Combine(externalRootPath, "inspection-files");

                if (!string.IsNullOrEmpty(req.AfterImageUrl) && !string.IsNullOrEmpty(req.AfterImageName))
                {
                    string uniqueFileName = $"{Guid.NewGuid()}_{req.AfterImageName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    string base64Data = req.AfterImageUrl.Contains(",")
                        ? req.AfterImageUrl.Substring(req.AfterImageUrl.IndexOf(",") + 1)
                        : req.AfterImageUrl;

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    req.AfterImageUrl = $"/ExternalFiles/inspection-files/{uniqueFileName}";
                }
                var result = await _service.UpdateInspectionItemStatusAsync(req);
                return StatusCode(result.Status, result);
            }
            catch (Exception)
            {

                throw;
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetInspectionCategory()
        {
            var result = await _service.GetInspectionCategoryAsync();
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetUserInspections(int userId)
        {
            var result = await _service.GetUserInspectionsAsync(userId);
            return StatusCode(result.Status, result);
        }
    }
} 