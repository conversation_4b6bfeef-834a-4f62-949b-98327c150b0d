﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstEventResponse
{
    public int ResponseId { get; set; }

    public int EventId { get; set; }

    public int UserId { get; set; }

    public bool? IsAccepted { get; set; }

    public DateTime? RespondedAt { get; set; }

    public virtual MstEvent Event { get; set; } = null!;

    public virtual MstUser User { get; set; } = null!;
}
