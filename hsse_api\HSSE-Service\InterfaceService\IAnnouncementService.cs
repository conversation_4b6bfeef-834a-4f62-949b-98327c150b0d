﻿using HSSE_ModelDto.ModelDto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IAnnouncementService
    {
        Task<ApiResponseDto<object>> InsertOrUpdateGroupAsync(MstGroupDto groupDto);
        Task<ApiResponseDto<object>> GetGroupsAsync();
        Task<ApiResponseDto<object>> GetGroupByIdAsync(int groupId);
        Task<ApiResponseDto<object>> DeleteGroupAsync(int groupId);

        Task<ApiResponseDto<object>> InsertOrUpdateGroupMemberAsync(MstGroupMemberDto groupMemberDto);
        Task<ApiResponseDto<object>> GetGroupMembersAsync();
        Task<ApiResponseDto<object>> GetGroupMemberByIdAsync(int groupMemberId);
        Task<ApiResponseDto<object>> DeleteAllGroupMembersByGroupIdAsync(int groupId);
       Task<ApiResponseDto<object>> GetGroupMembersByGroupIdAsync(int groupId);
        Task<ApiResponseDto<object>> InsertGroupMembersAsync(MstGroupMemberDto groupMemberDto);
        Task<ApiResponseDto<object>> UpdateGroupMembersAsync(MstGroupMemberDto groupMemberDto);


        Task<ApiResponseDto<object>> InsertOrUpdateAnnouncementAsync(CreateAnnouncementRequest request);
        Task<ApiResponseDto<object>> GetAnnouncementsAsync();
        Task<ApiResponseDto<object>> GetAnnouncementByIdAsync(int announcementId);
        Task<ApiResponseDto<object>> DeleteAnnouncementAsync(int announcementId);
        Task<ApiResponseDto<object>> UpdateAnnouncementAsync(CreateAnnouncementRequest request);
        Task<ApiResponseDto<object>> GetUsersAnnouncementsByUserIdAsync(int userId);

        Task<ApiResponseDto<object>> InsertAnnouncementReceiverAsync(MstAnnouncementReceiverDto dto);
        Task<ApiResponseDto<IEnumerable<object>>> GetAnnouncementReceiversAsync(int? userId = null, int? groupId = null, bool? delivered = null);
        Task<ApiResponseDto<object>> GetAnnouncementsByUserIdAsync(int userId);
        Task<ApiResponseDto<object>> GetAnnouncementDetailsByIdAsync(int announcementId);
        Task<ApiResponseDto<object>> ToggleAnnouncementAsync(int announcementId, int statusId);

        Task<ApiResponseDto<object>> InsertOrUpdateAnnouncementCategoryAsync(MstAnnoucementCategoryDto dto);
        Task<ApiResponseDto<object>> GetAnnouncementCategoriesByUserIdAsync(int userId);
        Task<ApiResponseDto<object>> GetAnnouncementCategoryByIdAsync(int categoryId);
        //Task<ApiResponseDto<object>> DeleteAnnouncementCategoryAsync(int categoryId);
        Task<ApiResponseDto<object>> ToggleAnnouncementCategoryStatusAsync(int categoryId);
        Task<ApiResponseDto<object>> GetAnnouncementCategoriesAsync();
    }
}
