﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstDeviceDetails
    {
        [Key]
        public int Id { get; set; }
        public int? FacilityID { get; set; }
        public string OSName { get; set; }
        public string AppVersion { get; set; }
        public DateTime? Datetime { get; set; }
        public string FCM { get; set; }
        public string Location { get; set; }
        public string DeviceUniqueID { get; set; }
        public string Lat { get; set; }
        public string Long { get; set; }
        public DateTime? CreatedAt { get; set; }
    }
}
