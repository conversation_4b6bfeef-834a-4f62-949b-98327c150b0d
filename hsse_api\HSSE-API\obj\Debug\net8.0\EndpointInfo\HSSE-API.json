{"openapi": "3.0.1", "info": {"title": "EntryPassUAT", "version": "v1"}, "paths": {"/api/Announcement/InsertOrUpdateGroup": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstGroupDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetGroups": {"get": {"tags": ["Announcement"], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetGroupById": {"get": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/DeleteGroup": {"delete": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/InsertOrUpdateGroupMember": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/InsertGroupMembers": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/UpdateGroupMembers": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstGroupMemberDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetGroupMembers": {"get": {"tags": ["Announcement"], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetGroupMembersByGroupId": {"get": {"tags": ["Announcement"], "parameters": [{"name": "groupId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetGroupMemberById": {"get": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/DeleteAllGroupMembersByGroupId": {"delete": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/InsertOrUpdateAnnouncement": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAnnouncementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAnnouncementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAnnouncementRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/UpdateAnnouncement": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAnnouncementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAnnouncementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAnnouncementRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncements": {"get": {"tags": ["Announcement"], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementsByUserId": {"get": {"tags": ["Announcement"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetUsersAnnouncementsByUserId": {"get": {"tags": ["Announcement"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementDetailsById": {"get": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementById": {"get": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/DeleteAnnouncement": {"delete": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/InsertAnnouncementReceiver": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstAnnouncementReceiverDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstAnnouncementReceiverDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstAnnouncementReceiverDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementReceivers": {"get": {"tags": ["Announcement"], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/ToggleAnnouncementStatus": {"post": {"tags": ["Announcement"], "parameters": [{"name": "announcementId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "statusId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/InsertOrUpdateAnnouncementCategory": {"post": {"tags": ["Announcement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstAnnoucementCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstAnnoucementCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstAnnoucementCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementCategoriesByUserId": {"get": {"tags": ["Announcement"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementCategories": {"get": {"tags": ["Announcement"], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/GetAnnouncementCategoryById": {"get": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Announcement/ToggleAnnouncementCategoryStatus": {"post": {"tags": ["Announcement"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/DocumentLibrary/CreateOrUpdateDocument": {"post": {"tags": ["DocumentLibrary"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstDocumentLibraryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstDocumentLibraryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstDocumentLibraryDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DocumentLibrary/GetDocuments": {"get": {"tags": ["DocumentLibrary"], "responses": {"200": {"description": "OK"}}}}, "/api/DocumentLibrary/GetDocumentsByUserId": {"get": {"tags": ["DocumentLibrary"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/DocumentLibrary/GetDocumentById": {"get": {"tags": ["DocumentLibrary"], "parameters": [{"name": "documentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/DocumentLibrary/DeleteDocument": {"delete": {"tags": ["DocumentLibrary"], "parameters": [{"name": "documentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "deletedBy", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/InsertOrUpdateEvent": {"post": {"tags": ["Event"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstEventDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstEventDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstEventDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Event/GetEvents": {"get": {"tags": ["Event"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/GetEventById": {"get": {"tags": ["Event"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/GetEventsByFacilityId": {"get": {"tags": ["Event"], "parameters": [{"name": "facilityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "dateFilterId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/SaveEventResponse": {"post": {"tags": ["Event"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstEventResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstEventResponseDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstEventResponseDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Event/GetEventResponsesByEventId": {"get": {"tags": ["Event"], "parameters": [{"name": "eventId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/SaveEventLike": {"post": {"tags": ["Event"], "parameters": [{"name": "eventId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/GetEventLikesByEventId": {"get": {"tags": ["Event"], "parameters": [{"name": "eventId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/ToggleEventActivation": {"post": {"tags": ["Event"], "parameters": [{"name": "eventId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Event/GetEventRsvpDetails": {"get": {"tags": ["Event"], "parameters": [{"name": "eventId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/CreateOrUpdateActionParty": {"post": {"tags": ["Inspection"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstActionPartyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstActionPartyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstActionPartyDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetActionPartyById": {"get": {"tags": ["Inspection"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetAllActionPartyByFacilityId": {"get": {"tags": ["Inspection"], "parameters": [{"name": "facilityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetActionPartyByUserId": {"get": {"tags": ["Inspection"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/ToggleActionPartiesActivation": {"post": {"tags": ["Inspection"], "parameters": [{"name": "actionPartyId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/CreateOrUpdateInspection": {"post": {"tags": ["Inspection"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstInspectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstInspectionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstInspectionDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetInspectionById": {"get": {"tags": ["Inspection"], "parameters": [{"name": "inspectionId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetInspectionsByActionParty": {"get": {"tags": ["Inspection"], "parameters": [{"name": "actionPartyName", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/UpdateInspectionItemStatus": {"post": {"tags": ["Inspection"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInspectionStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateInspectionStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateInspectionStatusDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetInspectionCategory": {"get": {"tags": ["Inspection"], "responses": {"200": {"description": "OK"}}}}, "/api/Inspection/GetUserInspections": {"get": {"tags": ["Inspection"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Login/ValidateToken": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Login/GetUserFacility": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Login/GetUserByFacilityId": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "facilityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/NewsLetter/InsertOrUpdateNewsLetter": {"post": {"tags": ["NewsLetter"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstNewsletterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstNewsletterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstNewsletterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/NewsLetter/GetNewsLettersByUserId": {"get": {"tags": ["NewsLetter"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/NewsLetter/GetNewsLettersById": {"get": {"tags": ["NewsLetter"], "parameters": [{"name": "newsletterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/NewsLetter/GetNewsLettersByFacilityId": {"get": {"tags": ["NewsLetter"], "parameters": [{"name": "facilityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/NewsLetter/DeleteNewsLetter": {"delete": {"tags": ["NewsLetter"], "parameters": [{"name": "newsLetterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/NewsLetter/ToggleNewsLetterActivation": {"post": {"tags": ["NewsLetter"], "parameters": [{"name": "newsLetterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Permission/GetPermissionsByUserId": {"get": {"tags": ["Permission"], "parameters": [{"name": "roleId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/CreatePost": {"post": {"tags": ["Posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstPostDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetPosts": {"get": {"tags": ["Posts"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "showOnlyMine", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/ToggleLike": {"post": {"tags": ["Posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstLikesConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstLikesConfigDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstLikesConfigDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Posts/InsertOrUpdatePostCategory": {"post": {"tags": ["Posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstPostCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstPostCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstPostCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetPostCategories": {"get": {"tags": ["Posts"], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetPostCategoryById": {"get": {"tags": ["Posts"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/DeletePostCategory": {"delete": {"tags": ["Posts"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetPostDetailsById": {"get": {"tags": ["Posts"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetPostDetails": {"get": {"tags": ["Posts"], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/InsertOrUpdatePostComment": {"post": {"tags": ["Posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstPostCommentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstPostCommentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstPostCommentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetPostCommentByPostId": {"get": {"tags": ["Posts"], "parameters": [{"name": "postId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/InsertOrUpdateFollowupPost": {"post": {"tags": ["Posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MstFollowupPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MstFollowupPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MstFollowupPostDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetAssignedPosts": {"get": {"tags": ["Posts"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/UpdateFollowUpStatus": {"post": {"tags": ["Posts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FollowupStatusUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FollowupStatusUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FollowupStatusUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Posts/GetAssignedUser": {"get": {"tags": ["Posts"], "parameters": [{"name": "postId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Posts/DeletePost": {"delete": {"tags": ["Posts"], "parameters": [{"name": "postId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "deletedBy", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CreateAnnouncementRequest": {"type": "object", "properties": {"announcementsId": {"type": "integer", "format": "int32", "nullable": true}, "sendTo": {"$ref": "#/components/schemas/SendToType"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "groupId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "scheduleAt": {"type": "string", "format": "date-time", "nullable": true}, "expiryAt": {"type": "string", "format": "date-time", "nullable": true}, "base64File": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "announcementDocument": {"type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32", "nullable": true}, "facilityId": {"type": "integer", "format": "int32", "nullable": true}, "categoryName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FollowupStatusUpdateRequestDto": {"type": "object", "properties": {"followupId": {"type": "integer", "format": "int32"}, "postId": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/PostStatus"}, "afterMediaUrl": {"type": "array", "items": {"type": "string"}, "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "completedComments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequestDto": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "facilityCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MstActionPartyDto": {"type": "object", "properties": {"actionPartyId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "facilityId": {"type": "integer", "format": "int32", "nullable": true}, "facilityName": {"type": "string", "nullable": true}, "userMappings": {"type": "array", "items": {"$ref": "#/components/schemas/MstActionPartyUserMappingDto"}, "nullable": true}}, "additionalProperties": false}, "MstActionPartyUserMappingDto": {"type": "object", "properties": {"mappingId": {"type": "integer", "format": "int32"}, "actionPartyId": {"type": "integer", "format": "int32", "nullable": true}, "userId": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MstAnnoucementCategoryDto": {"type": "object", "properties": {"annoucementCategoryId": {"type": "integer", "format": "int32"}, "annoucementCategoryName": {"type": "string", "nullable": true}, "status": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "MstAnnouncementReceiverDto": {"type": "object", "properties": {"receiverId": {"type": "integer", "format": "int32"}, "announcementId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "delivered": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "MstDocumentLibraryDto": {"type": "object", "properties": {"documentId": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "documentUrl": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "parentDocumentId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "MstEventDto": {"type": "object", "properties": {"eventId": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "mediaUrl": {"type": "string", "nullable": true}, "eventDateTime": {"type": "string", "format": "date-time", "nullable": true}, "location": {"type": "string", "nullable": true}, "externalLink": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "facilityId": {"type": "integer", "format": "int32", "nullable": true}, "likeCount": {"type": "integer", "format": "int32", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "groupId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "scheduleAt": {"type": "string", "format": "date-time", "nullable": true}, "expiryAt": {"type": "string", "format": "date-time", "nullable": true}, "isRsvp": {"type": "boolean"}, "mstEventResponses": {"type": "array", "items": {"$ref": "#/components/schemas/MstEventResponseDto"}, "nullable": true}, "mstLikesConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/MstLikesConfigDto"}, "nullable": true}}, "additionalProperties": false}, "MstEventResponseDto": {"type": "object", "properties": {"responseId": {"type": "integer", "format": "int32"}, "eventId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "isAccepted": {"type": "boolean", "nullable": true}, "respondedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstFollowupPostDto": {"type": "object", "properties": {"followupId": {"type": "integer", "format": "int32"}, "postId": {"type": "integer", "format": "int32"}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "format": {"type": "string", "nullable": true}, "assignedTo": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "assignedTime": {"type": "string", "format": "date-time", "nullable": true}, "followedupBy": {"type": "integer", "format": "int32", "nullable": true}, "followedupTime": {"type": "string", "format": "date-time", "nullable": true}, "completedBy": {"type": "integer", "format": "int32", "nullable": true}, "completedTime": {"type": "string", "format": "date-time", "nullable": true}, "archivedBy": {"type": "integer", "format": "int32", "nullable": true}, "archivedTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstGroupDto": {"type": "object", "properties": {"groupId": {"type": "integer", "format": "int32"}, "groupName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "createdBy": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "integer", "format": "int32", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstGroupMemberDto": {"type": "object", "properties": {"groupMemberId": {"type": "integer", "format": "int32"}, "groupId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "oldGroupId": {"type": "integer", "format": "int32", "nullable": true}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstInspectionDto": {"type": "object", "properties": {"inspectionId": {"type": "integer", "format": "int32"}, "facilityId": {"type": "integer", "format": "int32", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "inspectionDate": {"type": "string", "format": "date-time", "nullable": true}, "afterImagePath": {"type": "string", "nullable": true}, "referenceNo": {"type": "string", "nullable": true}, "typeOfInspection": {"type": "integer", "format": "int32", "nullable": true}, "typeOfInspectionName": {"type": "string", "nullable": true}, "actionPartyId": {"type": "integer", "format": "int32", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "specificLocation": {"type": "string", "nullable": true}, "inspectorName": {"type": "string", "nullable": true}, "verification": {"type": "integer", "format": "int32", "nullable": true}, "rectification": {"type": "string", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "completionDateTime": {"type": "string", "format": "date-time", "nullable": true}, "observation": {"type": "string", "nullable": true}, "recommendation": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "observationAttachmentBase64": {"type": "string", "nullable": true}, "observationAttachmentName": {"type": "string", "nullable": true}, "contactPersonId": {"type": "integer", "format": "int32", "nullable": true}, "contactPersonName": {"type": "string", "nullable": true}, "recommendationAttachmentBase64": {"type": "string", "nullable": true}, "recommendationAttachmentName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MstLikesConfigDto": {"type": "object", "properties": {"likeId": {"type": "integer", "format": "int32"}, "postId": {"type": "integer", "format": "int32", "nullable": true}, "eventId": {"type": "integer", "format": "int32", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "likedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstNewsletterDto": {"type": "object", "properties": {"newsletterId": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "thumbnailPath": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "facilityId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "fileName": {"type": "string", "nullable": true}, "scheduleAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstPostCategoryDto": {"type": "object", "properties": {"catId": {"type": "integer", "format": "int32"}, "categoryName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MstPostCommentDto": {"type": "object", "properties": {"commentId": {"type": "integer", "format": "int32"}, "postId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "commentText": {"type": "string", "nullable": true}, "commentedAt": {"type": "string", "format": "date-time", "nullable": true}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MstPostDto": {"type": "object", "properties": {"postId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "facilityId": {"type": "integer", "format": "int32", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "postType": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "taggedCategoryId": {"type": "integer", "format": "int32", "nullable": true}, "requiresFollowup": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "closedBy": {"type": "integer", "format": "int32", "nullable": true}, "deletedBy": {"type": "integer", "format": "int32", "nullable": true}, "likeCount": {"type": "integer", "format": "int32", "nullable": true}, "imageBase64": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "postCommentsCount": {"type": "integer", "format": "int32", "nullable": true}, "isLikedByUser": {"type": "boolean"}, "isAuthor": {"type": "boolean"}, "isAssigned": {"type": "boolean"}, "assignedToUsername": {"type": "array", "items": {"type": "string"}, "nullable": true}, "facilityName": {"type": "string", "nullable": true}, "assignedByUsername": {"type": "string", "nullable": true}, "completedBy": {"type": "string", "nullable": true}, "completedComments": {"type": "string", "nullable": true}, "afterMediaUrls": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "PostStatus": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "SendToType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "UpdateInspectionStatusDto": {"type": "object", "properties": {"inspectionId": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "completionDate": {"type": "string", "format": "date-time", "nullable": true}, "remarks": {"type": "string", "nullable": true}, "isVerified": {"type": "boolean"}, "afterImageUrl": {"type": "string", "nullable": true}, "afterImageName": {"type": "string", "nullable": true}, "changedBy": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer 1safsfsdfdfd\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}