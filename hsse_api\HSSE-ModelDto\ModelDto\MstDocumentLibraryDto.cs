using System;

namespace HSSE_ModelDto.ModelDto
{
    public class MstDocumentLibraryDto
    {
        public int DocumentId { get; set; }
        public string? Title { get; set; }
        public string? Category { get; set; }
        public string? Version { get; set; }
        public string? DocumentUrl { get; set; }
        public DateTime? Date { get; set; }
        public int? CreatedBy { get; set; }
        public int? ParentDocumentId { get; set; } // <-- Add this
    }
    public class DocumentHierarchyDto
    {
        public int DocumentId { get; set; }
        public string Title { get; set; }
        public string? Category { get; set; }
        public string? Version { get; set; }
        public string? DocumentUrl { get; set; }
        public DateTime? Date { get; set; }
        public List<DocumentHierarchyDto> Children { get; set; } = new();
    }

}