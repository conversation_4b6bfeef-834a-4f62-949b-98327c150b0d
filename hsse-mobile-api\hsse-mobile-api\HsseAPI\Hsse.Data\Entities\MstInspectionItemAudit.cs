﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstInspectionItemAudit
    {
        [Key]
        public int AuditId { get; set; }
        public int ItemId { get; set; }
        public string OldStatus { get; set; }
        public string NewStatus { get; set; }
        public string OldRectification { get; set; }
        public string NewRectification { get; set; }
        public string OldAfterImagePath { get; set; }
        public string NewAfterImagePath { get; set; }
        public int? ChangedBy { get; set; }
        public DateTime? ChangedAt { get; set; }
    }
}
