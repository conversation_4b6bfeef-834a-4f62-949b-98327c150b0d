﻿++Solution 'IFMMobileAPI' ‎ (4 of 4 projects)
i:{00000000-0000-0000-0000-000000000000}:IFMMobileAPI.sln
++InventoryMobile.API
++Connected Services 
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>969
++Dependencies
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>971
i:{29d86778-f021-4535-91a2-74eab1776eda}:>678
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>679
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>680
++Properties
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\properties\
++PublishProfiles
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\properties\publishprofiles\
++launchSettings.json
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\properties\launchsettings.json
++Controllers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\controllers\
++v1
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\controllers\v1\
++BaseAPIController.cs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\controllers\v1\baseapicontroller.cs
++FaultReportingController.cs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\controllers\v1\faultreportingcontroller.cs
++MasterController.cs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\controllers\v1\mastercontroller.cs
++PPMController.cs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\controllers\v1\ppmcontroller.cs
++Logs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\logs\
++log-20250326.log
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\logs\log-20250326.log
++appsettings.json
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\appsettings.json
++DependencyInjection.cs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\dependencyinjection.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\helper\dependencyinjection.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\dependencyinjection.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\helper\dependencyinjection.cs
++IFMMobile.API.http
++Program.cs
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\program.cs
++No service dependencies discovered
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>970
++Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1033
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1070
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1000
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>972
++Frameworks
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1056
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1087
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1017
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>989
++Packages
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1059
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1089
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1019
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>991
++Projects
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1030
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1067
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>998
++FolderProfile.pubxml
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\properties\publishprofiles\folderprofile.pubxml
++appsettings.Development.json
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\appsettings.development.json
++Microsoft.AspNetCore.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\sdk\9.0.102\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.0\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.0\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.0\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.0\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.12\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1057
++Microsoft.NETCore.App
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1058
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1088
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1018
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>990
++Asp.Versioning.Mvc (8.1.0)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1060
++Microsoft.EntityFrameworkCore (9.0.0)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1061
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1090
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1021
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>992
++Microsoft.EntityFrameworkCore.SqlServer (9.0.0)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1062
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1091
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1023
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>993
++Microsoft.EntityFrameworkCore.Tools (9.0.0)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1065
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1094
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1029
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>996
++Serilog.AspNetCore (9.0.0)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1064
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1092
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1025
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>995
++Swashbuckle.AspNetCore (6.4.0)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1063
++WindowsAzure.Storage (9.3.3)
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1066
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>997
++InventoryMobile.Application
++InventoryMobile.Data
++Helper
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\helper\
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\helper\
++IServices
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\iservices\
++IFaultReportingService.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\iservices\ifaultreportingservice.cs
++IMasterService.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\iservices\imasterservice.cs
++IPPMService.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\iservices\ippmservice.cs
++Services
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\services\
++FaultReportingService.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\services\faultreportingservice.cs
++MasterService.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\services\masterservice.cs
++PPMService.cs
i:{29d86778-f021-4535-91a2-74eab1776eda}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.application\services\ppmservice.cs
++Microsoft.Extensions.DependencyInjection.Abstractions (9.0.0)
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1093
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1026
++InventoryMobile.Infrastructure
++IRepositories
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\irepositories\
++Repositories
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\repositories\
++IFaultReportingRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\irepositories\ifaultreportingrepository.cs
++IMasterRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\irepositories\imasterrepository.cs
++INotificationRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\irepositories\inotificationrepository.cs
++IPPMRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\irepositories\ippmrepository.cs
++IStorageRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\irepositories\istoragerepository.cs
++FaultReportingRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\repositories\faultreportingrepository.cs
++MasterRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\repositories\masterrepository.cs
++NotificationRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\repositories\notificationrepository.cs
++PPMRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\repositories\ppmrepository.cs
++StorageRepository.cs
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.infrastructure\repositories\storagerepository.cs
++Dapper (2.1.35)
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1027
++Microsoft.Data.SqlClient (6.0.0-preview3.24332.3)
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1022
++Microsoft.Extensions.Configuration.Abstractions (9.0.0)
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1024
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:>994
++Microsoft.Extensions.Logging.Abstractions (9.0.0)
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1020
++System.Data.SqlClient (4.9.0)
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1028
++Dto
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\
++Request
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\
++Response
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\
++Entities
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\entities\
++MailTemplates
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\mailtemplates\
++AcknowledgePPMRequest.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\acknowledgeppmrequest.cs
++AckWORequestReqDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\ackworequestreqdto.cs
++AddOrRemovePPMReqDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\addorremoveppmreqdto.cs
++AddOrRemoveWOImageReqDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\addorremovewoimagereqdto.cs
++NotificationDetailsDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\notificationdetailsdto.cs
++PPMImageDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\ppmimagedto.cs
++SaveWorkOrderReqDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\saveworkorderreqdto.cs
++UpdatePPMReasonDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\updateppmreasondto.cs
++UpdatePPMRequestByStatusDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\updateppmrequestbystatusdto.cs
++UpdateReasonDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\updatereasondto.cs
++UpdateWOReasonReqDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\updateworeasonreqdto.cs
++UpdateWOStatusReqDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\updatewostatusreqdto.cs
++ValidateUserRequestDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\validateuserrequestdto.cs
++WOImageDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\request\woimagedto.cs
++DelayReasonDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\delayreasondto.cs
++FCMDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\fcmdto.cs
++GetAreabyLevelIDResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\getareabylevelidresdto.cs
++GetBlockbyFacilityIDResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\getblockbyfacilityidresdto.cs
++GetDashBoardInfoForTechnicianResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\getdashboardinfofortechnicianresdto.cs
++GetLevelByBlockIDResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\getlevelbyblockidresdto.cs
++GetRoombyAreaIDResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\getroombyareaidresdto.cs
++MstBranchDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\mstbranchdto.cs
++MstJobCategoryDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\mstjobcategorydto.cs
++MstJobTypeDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\mstjobtypedto.cs
++MstWorkPriorityDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\mstworkprioritydto.cs
++NotificationListResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\notificationlistresdto.cs
++PPMResponseDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\ppmresponsedto.cs
++ResponseDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\responsedto.cs
++UserWorkOrderRequestResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\userworkorderrequestresdto.cs
++ValidateUserResponseDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\validateuserresponsedto.cs
++WorkOrderEmailResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\workorderemailresdto.cs
++WorkOrderRequestResDto.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\dto\response\workorderrequestresdto.cs
++DtPPMImage.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\entities\dtppmimage.cs
++DtWOImage.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\entities\dtwoimage.cs
++MstGeneralSettings.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\entities\mstgeneralsettings.cs
++EmailHelper.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\helper\emailhelper.cs
++IFMDBContext.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\helper\ifmdbcontext.cs
++MasterDBContext.cs
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\helper\masterdbcontext.cs
++WOCompletionMailTemplate.htm
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\mailtemplates\wocompletionmailtemplate.htm
++WOCreationMailTemplate.htm
i:{a2cf399e-fbf2-47d6-879f-35044e7d853f}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.data\mailtemplates\wocreationmailtemplate.htm
++StoreInventory.API
i:{00000000-0000-0000-0000-000000000000}:StoreInventory.API
++StoreInventory.Application
i:{00000000-0000-0000-0000-000000000000}:StoreInventory.Application
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1327
++StoreInventory.Data
i:{00000000-0000-0000-0000-000000000000}:StoreInventory.Data
i:{4014331e-47fc-42b7-bc3c-ff08e9484223}:>1304
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1306
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:>1308
++StoreInventory.Infrastructure
i:{00000000-0000-0000-0000-000000000000}:StoreInventory.Infrastructure
i:{29d86778-f021-4535-91a2-74eab1776eda}:>1305
++StoreInventoryStoreInventory.Data
++StoreInventoryStoreInventory.Infrastructure
++StoreInventoryInventoryMobile.Application
++StoreInventoryAPI.http
++StoreInventory.API.http
i:{0cbdab25-e8cb-4334-974f-b6e95474fa5a}:c:\users\<USER>\desktop\codebase\sustainedge\inventory-api\ifmmobileapi\ifmmobile.api\storeinventory.api.http
++StoreInventoryInventory.Application
