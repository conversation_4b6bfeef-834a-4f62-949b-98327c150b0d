{"openapi": "3.0.1", "info": {"title": "IFMMobile.API", "version": "1.0"}, "paths": {"/api/v{version}/FaultReporting/GetBlockByFacilityID": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "facilityID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetLevelByBlockID": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "blockID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetAreaByLevelID": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "levelID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetRoomByAreaID": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "areaID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetBranchesByCustomerId": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "customerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetPriorities": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetJobTrades": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetJobTypesByTradeId": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "jobTradeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetDelayReason": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/AddWorkOrderRequest": {"post": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveWorkOrderReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveWorkOrderReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveWorkOrderReqDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/AddOrRemoveWOImages": {"post": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddOrRemoveWOImageReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddOrRemoveWOImageReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddOrRemoveWOImageReqDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetWorkOrderRequestsByTechnician": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "staffID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "facilityID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "roleID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "jobStatus", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetWorkOrderDetailsByReqID": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "reqId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/UpdateWOStatus": {"put": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWOStatusReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWOStatusReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWOStatusReqDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/UpdateWOReason": {"put": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWOReasonReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateWOReasonReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateWOReasonReqDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/AckWORequest": {"put": {"tags": ["FaultReporting"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AckWORequestReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AckWORequestReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AckWORequestReqDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetDashBoardInfoForTechnician": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "inputDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "facilityID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "staffID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/FaultReporting/GetNotificationListByStaff": {"get": {"tags": ["FaultReporting"], "parameters": [{"name": "staffID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Master/ValidateUser": {"post": {"tags": ["Master"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateUserRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidateUserRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidateUserRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/PPM/UpdatePPMRequestByStatus": {"post": {"tags": ["PPM"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePPMRequestByStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePPMRequestByStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePPMRequestByStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/PPM/GetPPMByTechnician": {"get": {"tags": ["PPM"], "parameters": [{"name": "StaffID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "FacilityID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "RoleID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "jobStatus", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/PPM/UpdateReasonByReqForPPM": {"post": {"tags": ["PPM"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePPMReasonDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePPMReasonDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePPMReasonDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/PPM/AddOrRemovePPMImages": {"post": {"tags": ["PPM"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddOrRemovePPMReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddOrRemovePPMReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddOrRemovePPMReqDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/PPM/AcknowledgePPMRequest": {"post": {"tags": ["PPM"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcknowledgePPMRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AcknowledgePPMRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AcknowledgePPMRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/PPM/UpdatePPMSurvey": {"post": {"tags": ["PPM"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcknowledgePPMRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AcknowledgePPMRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AcknowledgePPMRequest"}}}}, "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"AckWORequestReqDto": {"type": "object", "properties": {"reqID": {"type": "integer", "format": "int32", "nullable": true}, "staffID": {"type": "integer", "format": "int32", "nullable": true}, "facilityID": {"type": "integer", "format": "int32", "nullable": true}, "initial": {"type": "string", "nullable": true}, "signature": {"type": "string", "nullable": true}, "survey": {"type": "string", "nullable": true}, "remedy": {"type": "string", "nullable": true}, "ackType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AcknowledgePPMRequest": {"type": "object", "properties": {"pmid": {"type": "integer", "format": "int32"}, "staffID": {"type": "integer", "format": "int32"}, "signature": {"type": "string", "nullable": true}, "survey": {"type": "string", "nullable": true}, "facilityID": {"type": "integer", "format": "int32"}, "remedy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AddOrRemovePPMReqDto": {"type": "object", "properties": {"ppmId": {"type": "integer", "format": "int32", "nullable": true}, "ppmReqNo": {"type": "string", "nullable": true}, "imageStatus": {"type": "string", "nullable": true}, "addPPMImages": {"type": "array", "items": {"$ref": "#/components/schemas/PPMImageDto"}, "nullable": true}, "removePPMImages": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "AddOrRemoveWOImageReqDto": {"type": "object", "properties": {"reqId": {"type": "integer", "format": "int32", "nullable": true}, "woReqNo": {"type": "string", "nullable": true}, "imageStatus": {"type": "string", "nullable": true}, "addWOImages": {"type": "array", "items": {"$ref": "#/components/schemas/WOImageDto"}, "nullable": true}, "removeWOImages": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "PPMImageDto": {"type": "object", "properties": {"ppmid": {"type": "string", "nullable": true}, "imageStream": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "imagePath": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SaveWorkOrderReqDto": {"type": "object", "properties": {"reqPriority": {"type": "integer", "format": "int32", "nullable": true}, "reqJobCategory": {"type": "integer", "format": "int32", "nullable": true}, "reqJobType": {"type": "integer", "format": "int32", "nullable": true}, "reqLoc": {"type": "integer", "format": "int32", "nullable": true}, "reqDesc": {"type": "string", "nullable": true}, "reqBranchID": {"type": "integer", "format": "int32", "nullable": true}, "reqBy": {"type": "integer", "format": "int32", "nullable": true}, "phone": {"type": "string", "nullable": true}, "requested": {"type": "string", "nullable": true}, "woImages": {"type": "array", "items": {"$ref": "#/components/schemas/WOImageDto"}, "nullable": true}}, "additionalProperties": false}, "UpdatePPMReasonDto": {"type": "object", "properties": {"pmid": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "comments": {"type": "string", "nullable": true}, "userID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdatePPMRequestByStatusDto": {"type": "object", "properties": {"pmid": {"type": "integer", "format": "int32"}, "staffID": {"type": "integer", "format": "int32"}, "actionTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "facilityID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateWOReasonReqDto": {"type": "object", "properties": {"reqID": {"type": "integer", "format": "int32", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "userID": {"type": "integer", "format": "int32", "nullable": true}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateWOStatusReqDto": {"type": "object", "properties": {"reqID": {"type": "integer", "format": "int32", "nullable": true}, "roleId": {"type": "integer", "format": "int32", "nullable": true}, "staffID": {"type": "integer", "format": "int32", "nullable": true}, "actionTime": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "facilityID": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ValidateUserRequestDto": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "regID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WOImageDto": {"type": "object", "properties": {"wOreqID": {"type": "string", "nullable": true}, "imageStream": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "imagePath": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}