﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IStorageRepository
    {
        Task UploadBlob(string? base64String, string? imageName, string? module);
        Task<bool> CheckBlobExists(string? imageName, string? module);
        Task<bool> DeleteBlob(string? imageName, string? module);
    }
}
