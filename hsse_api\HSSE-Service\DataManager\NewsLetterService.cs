using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class NewsLetterService : INewsLetterService
    {
        private readonly HsseDbLatestContext _context;
        private readonly IMapper _mapper;

        public NewsLetterService(HsseDbLatestContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateNewsLetterAsync(MstNewsletterDto dto)
        {
            try
            {
                MstNewsletter entity;
                bool isUpdate = dto.NewsletterId > 0;
                if (isUpdate)
                {
                    entity = await _context.MstNewsletters.FindAsync(dto.NewsletterId);
                    if (entity == null)
                    {
                        return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                    }
                    string existingThumbnailPath = entity.ThumbnailPath;

                    _mapper.Map(dto, entity);
                    // Restore the original ThumbnailPath if the new value is null
                    if (dto.ThumbnailPath == null)
                    {
                        entity.ThumbnailPath = existingThumbnailPath;
                    }

                    _context.MstNewsletters.Update(entity);
                }
                else
                {
                    entity = _mapper.Map<MstNewsletter>(dto);
                    _context.MstNewsletters.Add(entity);
                }
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(isUpdate ? AppMessages.NewsletterUpdatedSuccessfully : AppMessages.NewsletterCreatedSuccessfully, StatusCodes.Status200OK, _mapper.Map<MstNewsletterDto>(entity));
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetNewsLettersByUserIdAsync(int userId)
        {
            try
            {
                var newsletters = await _context.MstNewsletters.Where(x => x.CreatedBy == userId).ToListAsync();
                var dtos = _mapper.Map<List<MstNewsletterDto>>(newsletters);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterRetrieved, StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetNewsLettersByIdAsync(int newsletterId)
        {
            try
            {
                var newsletters = await _context.MstNewsletters.FirstOrDefaultAsync(x => x.NewsletterId == newsletterId);
                var dtos = _mapper.Map<MstNewsletterDto>(newsletters);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterRetrieved, StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetNewsLettersByFacilityIdAsync(int facilityId, int userId)
        {
            try
            {
                DateTime date = DateTime.Now;
                var newsletters = await _context.MstNewsletters
       .Where(x =>
           x.FacilityId == facilityId &&
           x.CreatedBy != userId &&
           x.IsActive &&
           (
               !x.ScheduleAt.HasValue || // ScheduleAt is null
               x.ScheduleAt.Value.Date == date.Date // or matches given date
           )
       )
       .ToListAsync();

                var dtos = _mapper.Map<List<MstNewsletterDto>>(newsletters);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterRetrieved, StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> DeleteNewsLetterAsync(int newsLetterId)
        {
            try
            {
                var entity = await _context.MstNewsletters.FindAsync(newsLetterId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                }
                _context.MstNewsletters.Remove(entity);
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterDeletedSuccessfully, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> ToggleNewsLetterActivationAsync(int newsLetterId)
        {
            try
            {
                var entity = await _context.MstNewsletters.FindAsync(newsLetterId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                }
                entity.IsActive = !entity.IsActive;
                await _context.SaveChangesAsync();
                var message = entity.IsActive ? AppMessages.NewsletterActivatedSuccessfully : AppMessages.NewsletterDeactivatedSuccessfully;
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK, entity.IsActive);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
    }
} 