using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class DocumentLibraryService : IDocumentLibraryService
    {
        private readonly HsseDbLatestContext _dbContext;
        private readonly IMapper _mapper;

        public DocumentLibraryService(HsseDbLatestContext dbContext, IMapper mapper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<ApiResponseDto<MstDocumentLibraryDto>> InsertOrUpdateDocumentAsync(MstDocumentLibraryDto dto)
        {
            try
            {
                MstDocumentLibrary entity;
                if (dto.DocumentId > 0)
                {
                    entity = await _dbContext.MstDocumentLibraries.FindAsync(dto.DocumentId);
                    if (entity == null)
                    {
                        return ApiResponseDto<MstDocumentLibraryDto>.ErrorResponse("Document not found", StatusCodes.Status404NotFound);
                    }
                    _mapper.Map(dto, entity);
                    entity.Date = DateTime.Now;
                    _dbContext.MstDocumentLibraries.Update(entity);
                }
                else
                {
                    entity = _mapper.Map<MstDocumentLibrary>(dto);
                    entity.Date = DateTime.Now;
                    _dbContext.MstDocumentLibraries.Add(entity);
                }
                await _dbContext.SaveChangesAsync();
                var resultDto = _mapper.Map<MstDocumentLibraryDto>(entity);
                return ApiResponseDto<MstDocumentLibraryDto>.SuccessResponse("Document saved successfully", StatusCodes.Status200OK, resultDto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstDocumentLibraryDto>.ErrorResponse($"Error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<List<DocumentHierarchyDto>>> GetDocumentsAsync()
        {
            try
            {
                var entities = await _dbContext.MstDocumentLibraries
                    .Where(x => !x.IsDeleted)
                    .ToListAsync();

                // Step 1: Convert to a dictionary of DTOs
                var allDtos = entities.Select(e => new DocumentHierarchyDto
                {
                    DocumentId = e.DocumentId,
                    Title = e.Title,
                    Category = e.Category,
                    Version = e.Version,
                    DocumentUrl = e.DocumentUrl,
                    Date = e.Date
                }).ToList();

                var lookup = allDtos.ToDictionary(x => x.DocumentId);
                var roots = new List<DocumentHierarchyDto>();

                foreach (var entity in entities)
                {
                    if (entity.ParentDocumentId.HasValue && lookup.ContainsKey(entity.ParentDocumentId.Value))
                    {
                        lookup[entity.ParentDocumentId.Value].Children.Add(lookup[entity.DocumentId]);
                    }
                    else
                    {
                        roots.Add(lookup[entity.DocumentId]);
                    }
                }

                return ApiResponseDto<List<DocumentHierarchyDto>>.SuccessResponse("Documents retrieved successfully", StatusCodes.Status200OK, roots);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<DocumentHierarchyDto>>.ErrorResponse($"Error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }


        public async Task<ApiResponseDto<List<DocumentHierarchyDto>>> GetDocumentsByUserIdAsync(int userId)
        {
            try
            {
                var entities = await _dbContext.MstDocumentLibraries.Where(x => x.CreatedBy == userId && !x.IsDeleted).ToListAsync();
                // Step 1: Convert to a dictionary of DTOs
                var allDtos = entities.Select(e => new DocumentHierarchyDto
                {
                    DocumentId = e.DocumentId,
                    Title = e.Title,
                    Category = e.Category,
                    Version = e.Version,
                    DocumentUrl = e.DocumentUrl,
                    Date = e.Date
                }).ToList();

                var lookup = allDtos.ToDictionary(x => x.DocumentId);
                var roots = new List<DocumentHierarchyDto>();

                foreach (var entity in entities)
                {
                    if (entity.ParentDocumentId.HasValue && lookup.ContainsKey(entity.ParentDocumentId.Value))
                    {
                        lookup[entity.ParentDocumentId.Value].Children.Add(lookup[entity.DocumentId]);
                    }
                    else
                    {
                        roots.Add(lookup[entity.DocumentId]);
                    }
                }

                return ApiResponseDto<List<DocumentHierarchyDto>>.SuccessResponse("Documents retrieved successfully", StatusCodes.Status200OK, roots);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<DocumentHierarchyDto>>.ErrorResponse($"Error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<MstDocumentLibraryDto>> GetDocumentByIdAsync(int documentId)
        {
            try
            {
                var entity = await _dbContext.MstDocumentLibraries.Where(d => !d.IsDeleted && d.DocumentId == documentId).FirstOrDefaultAsync();
                if (entity == null)
                {
                    return ApiResponseDto<MstDocumentLibraryDto>.ErrorResponse("Document not found", StatusCodes.Status404NotFound);
                }
                var dto = _mapper.Map<MstDocumentLibraryDto>(entity);
                return ApiResponseDto<MstDocumentLibraryDto>.SuccessResponse("Document retrieved successfully", StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstDocumentLibraryDto>.ErrorResponse($"Error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> DeleteDocumentAsync(int documentId, int deletedBy)
        {
            try
            {
                var post = await _dbContext.MstDocumentLibraries.FindAsync(documentId);

                if (post == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Document not found", StatusCodes.Status404NotFound);
                }

                if (post.IsDeleted)
                {
                    return ApiResponseDto<object>.ErrorResponse("eDocument is already deleted", StatusCodes.Status400BadRequest);
                }

                // Perform soft delete
                post.IsDeleted = true;
                post.DeletedBy = deletedBy;

                _dbContext.MstDocumentLibraries.Update(post);
                await _dbContext.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse("Document deleted successfully", StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                // Optionally log the exception here
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

    }
} 