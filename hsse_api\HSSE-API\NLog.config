﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Error"
	  throwExceptions="false">

  <!--https://nlog-project.org/config/?tab=layout-renderers-->

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <!-- the targets to write to -->
  <targets>
    <!-- write logs to file  -->
    <target xsi:type="File" name="target1" fileName="${var:mydir}/${shortdate}_logfile.txt"
		  layout="${longdate} ${level:uppercase=true} ${message}" />
    <target xsi:type="Console" name="target2"
				layout="${longdate} ${level:uppercase=true} ${message}" />

  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
	  <logger name="*" minlevel="Info" maxlevel="Info" writeTo="target1,target2" />
	  <logger name="*" minlevel="Error" writeTo="target1,target2" />
	  <logger name="*" minlevel="Warning" writeTo="target1,target2" />
	  <logger name="*" minlevel="Debug" writeTo="target1,target2" />

  </rules>

</nlog>