﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstGroup
{
    public int GroupId { get; set; }

    public string GroupName { get; set; } = null!;

    public string? Description { get; set; }

    public int CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public virtual MstUser CreatedByNavigation { get; set; } = null!;

    public virtual ICollection<MstAnnouncementReceiver> MstAnnouncementReceivers { get; set; } = new List<MstAnnouncementReceiver>();

    public virtual ICollection<MstGroupMember> MstGroupMembers { get; set; } = new List<MstGroupMember>();
}
