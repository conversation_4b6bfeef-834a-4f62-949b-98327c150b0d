﻿using System;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Helper
{
    public class EmailHelper
    {
        private readonly IConfiguration _configuration;

        public EmailHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task SendEmailAsync(string? toEmail, string subject, string body)
        {
            if (string.IsNullOrEmpty(toEmail))
                return;

            // Retrieve SMTP settings from appsettings.json
            var fromEmail = _configuration["EmailSettings:FromEmail"];
            var smtpHost = _configuration["EmailSettings:SmtpHost"];
            var smtpPort = int.Parse(_configuration["EmailSettings:SmtpPort"] ?? "587");
            var smtpUser = _configuration["EmailSettings:SmtpUser"];
            var smtpPass = _configuration["EmailSettings:SmtpPass"];

            var mailMessage = new MailMessage
            {
                From = new MailAddress(fromEmail ?? "", "UETrack - IFM"),
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };

            var toEmailList = toEmail.Split(",").ToList();
            foreach (var email in toEmailList)
            {
                mailMessage.To.Add(email);
            }

            using var smtpClient = new SmtpClient(smtpHost, smtpPort)
            {
                Credentials = new NetworkCredential(smtpUser, smtpPass),
                EnableSsl = true
            };

            await smtpClient.SendMailAsync(mailMessage);
        }
    }

}
