﻿using Dapper;
using Hsse.Data.Dto.Response;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Infrastructure.IRepositories;
using Microsoft.Data.SqlClient;
using System.Reflection;
using Microsoft.Extensions.Logging;
using Serilog.Core;

namespace Hsse.Infrastructure.Repositories
{
    public class LoginRepository : ILoginRepository
    {
        private readonly IConfiguration _IConfiguration;
        private IDbConnection db;
        private readonly ILogger<LoginRepository> _logger;
        private readonly string _connectionString;

        public LoginRepository(IConfiguration IConfiguration, ILogger<LoginRepository> logger)
        {
            _IConfiguration = IConfiguration;
            _logger = logger;
            _connectionString = _IConfiguration.GetConnectionString("HsseDB");
        }

    }
}
