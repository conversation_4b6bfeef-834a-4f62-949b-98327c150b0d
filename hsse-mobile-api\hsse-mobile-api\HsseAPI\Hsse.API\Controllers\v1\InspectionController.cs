﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class InspectionController : ControllerBase
    {
        private readonly IInspectionService _IInspectionService;
        private readonly ILogger<InspectionController> _logger;
        public InspectionController(IInspectionService inspectionService, IConfiguration configuration, ILogger<InspectionController> logger)
        {
            _logger = logger;
            _IInspectionService = inspectionService;
        }
    }
}
