﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstInspectionItemAudit
{
    public int AuditId { get; set; }

    public int ItemId { get; set; }

    public string? OldStatus { get; set; }

    public string? NewStatus { get; set; }

    public string? OldRectification { get; set; }

    public string? NewRectification { get; set; }

    public string? OldAfterImagePath { get; set; }

    public string? NewAfterImagePath { get; set; }

    public int? ChangedBy { get; set; }

    public DateTime? ChangedAt { get; set; }

    public virtual MstInspectionItem Item { get; set; } = null!;
}
