﻿using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Hsse.Data.Dto.Response;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Hsse.Infrastructure.Helpers
{
    public class AuthorizeHelper
    {
        private readonly IConfiguration _configuration;

        public AuthorizeHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string GenerateToken(EmployeeDetails employeeDetails)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");

            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings["Key"]));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, employeeDetails.EmailID ?? string.Empty),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim("EmployeeId", employeeDetails.EmployeeId.ToString()),
                new Claim("EmailId", employeeDetails.EmailID ?? string.Empty),
                new Claim("Name", employeeDetails.EmployeeName ?? string.Empty),
                new Claim("Role", employeeDetails.Role ?? string.Empty)
            };

            var token = new JwtSecurityToken(
                issuer: jwtSettings["Issuer"],
                audience: jwtSettings["Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(Convert.ToDouble(jwtSettings["ExpiresInMinutes"])),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}


