2025-05-30 17:00:02.462 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-30 17:00:03.170 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-30 17:00:03.173 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-30 17:00:03.392 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-30 17:00:03.396 +05:30 [INF] Hosting environment: Development
2025-05-30 17:00:03.398 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-30 17:00:06.010 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger - null null
2025-05-30 17:00:06.279 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger - 301 0 null 285.7296ms
2025-05-30 17:00:06.288 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-30 17:00:06.380 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 92.2273ms
2025-05-30 17:00:06.393 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/swagger-ui.css - null null
2025-05-30 17:00:06.393 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/swagger-ui-bundle.js - null null
2025-05-30 17:00:06.395 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/swagger-ui-standalone-preset.js - null null
2025-05-30 17:00:06.398 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-30 17:00:06.438 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-30 17:00:06.458 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 59.9149ms
2025-05-30 17:00:06.523 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-30 17:00:06.573 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-30 17:00:06.600 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/swagger-ui.css - 200 143943 text/css 206.888ms
2025-05-30 17:00:06.632 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-30 17:00:06.616 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/swagger-ui-standalone-preset.js - 200 339486 text/javascript 220.4074ms
2025-05-30 17:00:06.656 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/swagger-ui-bundle.js - 200 1096145 text/javascript 262.1341ms
2025-05-30 17:00:06.658 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 220.219ms
2025-05-30 17:00:07.443 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-30 17:00:07.495 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/favicon-32x32.png - null null
2025-05-30 17:00:07.496 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 52.8409ms
2025-05-30 17:00:07.588 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-30 17:00:07.602 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/favicon-32x32.png - 200 628 image/png 108.0474ms
