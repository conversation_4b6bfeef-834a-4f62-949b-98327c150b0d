﻿using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class ApiLoggerService: IApiLoggerService
    {
        private readonly HsseDbLatestContext _context;

        public ApiLoggerService(HsseDbLatestContext context)
        {
            _context = context;
        }

        public async Task LogInfo(LogsDto log)
        {
            await SaveLog(log, "Info");
        }

        public async Task LogError(LogsDto log)
        {
            await SaveLog(log, "Error");
        }

        private async Task SaveLog(LogsDto log, string level)
        {
            try
            {
                var entity = new MstApiLog
                {
                    CreatedOn = log.CreatedOn,
                    Level = level,
                    Message = log.Message,
                    StackTrace = log.StackTrace,
                    Exception = log.Exception,
                    Logger = log.Logger,
                    Url = log.Url
                };

                _context.MstApiLogs.Add(entity);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
    }
}
