﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_ModelDto.ModelDto
{
    public class PermissionDto
    {
        public int PermissionId { get; set; }
        public int? ParentMenuId { get; set; }
        public string MenuName { get; set; }
        public string? ControllerName { get; set; }
        public string? ActionName { get; set; }
        public string? AreaName { get; set; }
        public string? RouteParams { get; set; }
        public string? Icon { get; set; }
        public int? OrderNo { get; set; }
        public bool? IsActive { get; set; }

        public bool? CanView { get; set; }
        public bool? CanCreate { get; set; }
        public bool? CanEdit { get; set; }
        public bool? CanDelete { get; set; }

        public List<PermissionDto> Children { get; set; } = new();
    }

}
