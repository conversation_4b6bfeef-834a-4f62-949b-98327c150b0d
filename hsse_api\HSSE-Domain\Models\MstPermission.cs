﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstPermission
{
    public int PermissionId { get; set; }

    public int? ParentMenuId { get; set; }

    public string? MenuName { get; set; }

    public string? ControllerName { get; set; }

    public string? ActionName { get; set; }

    public string? AreaName { get; set; }

    public string? RouteParams { get; set; }

    public string? Icon { get; set; }

    public int? OrderNo { get; set; }

    public bool? IsActive { get; set; }

    public virtual ICollection<MstRoleMenuPermission> MstRoleMenuPermissions { get; set; } = new List<MstRoleMenuPermission>();
}
