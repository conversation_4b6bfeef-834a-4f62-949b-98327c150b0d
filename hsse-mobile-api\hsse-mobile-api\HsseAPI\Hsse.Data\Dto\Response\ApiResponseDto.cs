using Newtonsoft.Json;
using System.Collections.Generic;

namespace Hsse.Data.Dto.Response
{
    public class ApiResponseDto<T>
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;

        [JsonProperty("status")]
        public int Status { get; set; }

        [JsonProperty("data")]
        public T? Data { get; set; }

        [JsonProperty("errors")]
        public List<string>? Errors { get; set; }

        public static ApiResponseDto<T> SuccessResponse(string message, int status)
        {
            return new ApiResponseDto<T> 
            { 
                Success = true, 
                Message = message, 
                Status = status 
            };
        }

        public static ApiResponseDto<T> SuccessResponse(string message, int status, T data)
        {
            return new ApiResponseDto<T> 
            { 
                Success = true, 
                Message = message, 
                Data = data, 
                Status = status 
            };
        }

        public static ApiResponseDto<T> ErrorResponse(string message, int status)
        {
            return new ApiResponseDto<T> 
            { 
                Success = false, 
                Message = message, 
                Status = status 
            };
        }

        public static ApiResponseDto<T> ErrorResponse(string message, int status, List<string> errors)
        {
            return new ApiResponseDto<T> 
            { 
                Success = false, 
                Message = message, 
                Status = status, 
                Errors = errors 
            };
        }

        public static ApiResponseDto<T> ValidationErrorResponse(string message, List<string> errors)
        {
            return new ApiResponseDto<T> 
            { 
                Success = false, 
                Message = message, 
                Status = 400, 
                Errors = errors 
            };
        }
    }
}
