{"openapi": "3.0.1", "info": {"title": "Hsse API", "description": "API for managing store inventory", "version": "v1"}, "paths": {"/api/v{version}/Login/ValidateUser": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "employeeEmail", "in": "query", "schema": {"type": "string"}}, {"name": "employeeId", "in": "query", "schema": {"type": "string"}}, {"name": "fCMToken", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Login/LogoutUser": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Post/CreatePost": {"post": {"tags": ["Post"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePostDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Post/GetPosts": {"get": {"tags": ["Post"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Post/GetPostCategories": {"get": {"tags": ["Post"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Post/CreateOrUpdateLikes": {"post": {"tags": ["Post"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLikeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateLikeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateLikeDto"}}}}, "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"CreateLikeDto": {"type": "object", "properties": {"likeID": {"type": "integer", "format": "int32"}, "postID": {"type": "integer", "format": "int32", "nullable": true}, "eventID": {"type": "integer", "format": "int32", "nullable": true}, "userID": {"type": "integer", "format": "int32"}, "isLiked": {"type": "boolean"}}, "additionalProperties": false}, "CreatePostDto": {"type": "object", "properties": {"postID": {"type": "integer", "format": "int32"}, "userID": {"type": "integer", "format": "int32"}, "facilityID": {"type": "integer", "format": "int32", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "postType": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "taggedCategoryId": {"type": "integer", "format": "int32", "nullable": true}, "requiresFollowup": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "mediaID": {"type": "integer", "format": "int32"}, "mediaURL": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Enter 'Bearer' [space] and then your token in the text input below.\n\nExample: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}