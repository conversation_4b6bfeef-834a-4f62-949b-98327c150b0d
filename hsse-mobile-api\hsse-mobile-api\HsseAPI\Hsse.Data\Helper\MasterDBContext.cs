﻿using Hsse.Data.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Helper
{
    public class MasterDBContext : DbContext
    {
        public MasterDBContext(DbContextOptions<MasterDBContext> options):base(options)
        {

        }

        public DbSet<MstActionParty> MstActionParty { get; set; }
        public DbSet<MstActionPartyUserMapping> MstActionPartyUserMapping { get; set; }
        public DbSet<MstAnnouncementCategory> MstAnnouncementCategory { get; set; }
        public DbSet<MstAnnouncementDocuments> MstAnnouncementDocuments { get; set; }
        public DbSet<MstAnnouncementReceivers> MstAnnouncementReceivers { get; set; }
        public DbSet<MstAnnouncements> MstAnnouncements { get; set; }
        public DbSet<MstApiLogs> MstApiLogs { get; set; }
        public DbSet<MstAuditLogs> MstAuditLogs { get; set; }
        public DbSet<MstDeviceDetails> MstDeviceDetails { get; set; }
        public DbSet<MstDocumentLibrary> MstDocumentLibrary { get; set; }
        public DbSet<MstEventResponses> MstEventResponses { get; set; }
        public DbSet<MstEvents> MstEvents { get; set; }
        public DbSet<MstFacilities> MstFacilities { get; set; }
        public DbSet<MstFollowupPosts> MstFollowupPosts { get; set; }
        public DbSet<MstGroupMembers> MstGroupMembers { get; set; }
        public DbSet<MstGroups> MstGroups { get; set; }
        public DbSet<MstInspectionCategory> MstInspectionCategory { get; set; }
        public DbSet<MstInspectionItemAudit> MstInspectionItemAudit { get; set; }
        public DbSet<MstInspectionItemComments> MstInspectionItemComments { get; set; }
        public DbSet<MstInspectionItems> MstInspectionItems { get; set; }
        public DbSet<MstInspections> MstInspections { get; set; }
        public DbSet<MstLikesConfig> MstLikesConfig { get; set; }
        public DbSet<MstNewsletter> MstNewsletter { get; set; }
        public DbSet<MstNotifications> MstNotifications { get; set; }
        public DbSet<MstOrganisations> MstOrganisations { get; set; }
        public DbSet<MstPermission> MstPermission { get; set; }
        public DbSet<MstPostCategories> MstPostCategories { get; set; }
        public DbSet<MstPostComments> MstPostComments { get; set; }
        public DbSet<MstPostMedia> MstPostMedia { get; set; }
        public DbSet<MstPosts> MstPosts { get; set; }
        public DbSet<MstPostStatus> MstPostStatus { get; set; }

    }
}
