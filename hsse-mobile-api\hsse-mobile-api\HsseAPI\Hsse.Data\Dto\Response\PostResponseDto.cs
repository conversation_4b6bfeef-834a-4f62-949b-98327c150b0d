using System;
using System.Collections.Generic;

namespace Hsse.Data.Dto.Response
{
    public class PostResponseDto
    {
        public int PostId { get; set; }
        public int UserId { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
        public int? FacilityId { get; set; }
        public string? FacilityName { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? PostType { get; set; }
        public string? Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public string? CategoryName { get; set; }
        public bool RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public string? StatusName { get; set; }
        public string? MediaUrl { get; set; }
        public List<int>? AssignedTo { get; set; }
        public List<string>? AssignedUserNames { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Priority { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? LikeCount { get; set; }
        public int? CommentCount { get; set; }
        public bool IsLikedByCurrentUser { get; set; }
        public List<PostCommentResponseDto>? Comments { get; set; }
        public List<FollowupPostResponseDto>? Followups { get; set; }
    }

    public class PostCommentResponseDto
    {
        public int CommentId { get; set; }
        public int PostId { get; set; }
        public int UserId { get; set; }
        public string? UserName { get; set; }
        public string Comment { get; set; } = string.Empty;
        public int? ParentCommentId { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public List<PostCommentResponseDto>? Replies { get; set; }
    }

    public class FollowupPostResponseDto
    {
        public int FollowupId { get; set; }
        public int PostId { get; set; }
        public int AssignedTo { get; set; }
        public string? AssignedToName { get; set; }
        public int AssignedBy { get; set; }
        public string? AssignedByName { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Priority { get; set; }
        public string? Status { get; set; }
        public string? Comments { get; set; }
        public List<string>? BeforeMediaUrl { get; set; }
        public List<string>? AfterMediaUrl { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime? CreatedAt { get; set; }
        public bool IsActive { get; set; }
    }

    public class PostCategoryResponseDto
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? CreatedBy { get; set; }
        public string? CreatedByName { get; set; }
        public int PostCount { get; set; }
    }

    public class PostListResponseDto
    {
        public List<PostResponseDto> Posts { get; set; } = new List<PostResponseDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    public class AssignedUserResponseDto
    {
        public int UserId { get; set; }
        public string? UserName { get; set; }
        public string? Email { get; set; }
        public string? Role { get; set; }
        public int? FacilityId { get; set; }
        public string? FacilityName { get; set; }
    }
}
