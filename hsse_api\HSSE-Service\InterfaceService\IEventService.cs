using HSSE_ModelDto.ModelDto;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IEventService
    {
        Task<ApiResponseDto<object>> InsertOrUpdateEventAsync(MstEventDto eventDto);
        Task<ApiResponseDto<object>> GetEventsAsync(int userId);
        Task<ApiResponseDto<object>> GetEventByIdAsync(int eventId);
        //Task<ApiResponseDto<object>> GetEventsByFacilityIdAsync(int facilityId, int userId);
        Task<ApiResponseDto<object>> SaveEventResponseAsync(MstEventResponseDto responseDto);
        Task<ApiResponseDto<object>> GetEventResponsesByEventIdAsync(int eventId);
        Task<ApiResponseDto<object>> SaveEventLikeAsync(int eventId, int userId);
        Task<ApiResponseDto<object>> GetEventLikesByEventIdAsync(int eventId);
        Task<ApiResponseDto<object>> ToggleEventActivationAsync(int eventId);
        Task<ApiResponseDto<object>> GetEventsByFacilityIdAsync(
          int facilityId,
          int userId,
          string? search = null,
    int dateFilterId = 0     // pass the filter id from frontend
      );
        Task<ApiResponseDto<object>> GetEventRsvpDetailsAsync(int eventId);
    }
} 