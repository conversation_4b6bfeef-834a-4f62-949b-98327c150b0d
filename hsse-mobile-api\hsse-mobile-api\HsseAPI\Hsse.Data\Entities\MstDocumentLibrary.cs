﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstDocumentLibrary
    {
        [Key]
        public int DocumentId { get; set; }
        public string Title { get; set; }
        public string Category { get; set; }
        public string Version { get; set; }
        public string DocumentUrl { get; set; }
        public DateTime? Date { get; set; }
        public int? CreatedBy { get; set; }
        public bool IsDeleted { get; set; }
        public int? DeletedBy { get; set; }
        public int? ParentDocumentId { get; set; }
    }
}
