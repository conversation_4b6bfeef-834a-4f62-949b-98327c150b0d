﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface IPostService
    {
        // Post CRUD operations
        Task<ApiResponseDto<int>> CreatePostAsync(CreatePostDto postDto);
        Task<ApiResponseDto<bool>> UpdatePostAsync(UpdatePostDto postDto);
        Task<ApiResponseDto<bool>> DeletePostAsync(int postId, int deletedBy);
        Task<ApiResponseDto<PostResponseDto>> GetPostByIdAsync(int postId, int? currentUserId = null);
        Task<ApiResponseDto<List<PostResponseDto>>> GetPostsAsync(int? userId = null, bool? showOnlyMine = null, int? facilityId = null, int pageNumber = 1, int pageSize = 10);
        Task<ApiResponseDto<List<PostResponseDto>>> GetAssignedPostsAsync(int? userId, int? status = null);

        // Post Categories
        Task<ApiResponseDto<int>> CreateOrUpdatePostCategoryAsync(PostCategoryDto categoryDto);
        Task<ApiResponseDto<bool>> DeletePostCategoryAsync(int categoryId);
        Task<ApiResponseDto<PostCategoryResponseDto>> GetPostCategoryByIdAsync(int categoryId);
        Task<ApiResponseDto<List<PostCategoryResponseDto>>> GetPostCategoriesAsync();

        // Post Comments
        Task<ApiResponseDto<int>> CreateOrUpdatePostCommentAsync(PostCommentDto commentDto);
        Task<ApiResponseDto<List<PostCommentResponseDto>>> GetPostCommentsAsync(int postId);

        // Post Likes
        Task<ApiResponseDto<bool>> ToggleLikeAsync(int postId, int userId);
        Task<ApiResponseDto<int>> GetLikeCountAsync(int postId);
        Task<ApiResponseDto<bool>> IsLikedByUserAsync(int postId, int userId);

        // Followup Posts
        Task<ApiResponseDto<int>> CreateOrUpdateFollowupPostAsync(FollowupPostDto followupDto);
        Task<ApiResponseDto<bool>> UpdateFollowupStatusAsync(FollowupStatusUpdateDto statusUpdateDto);
        Task<ApiResponseDto<List<FollowupPostResponseDto>>> GetFollowupsByPostIdAsync(int postId);
        Task<ApiResponseDto<List<AssignedUserResponseDto>>> GetAssignedUsersAsync(int postId);

        // Statistics
        Task<ApiResponseDto<int>> GetTotalPostCountAsync(int? facilityId = null);
        Task<ApiResponseDto<int>> GetActivePostCountAsync(int? facilityId = null);
        Task<ApiResponseDto<int>> GetPendingFollowupCountAsync(int? userId = null);
    }
}
