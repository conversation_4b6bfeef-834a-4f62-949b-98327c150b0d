﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstAnnouncement
{
    public int AnnouncementsId { get; set; }

    public string Title { get; set; } = null!;

    public string Description { get; set; } = null!;

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public string? AnnouncementDocument { get; set; }

    public DateTime? ScheduleAt { get; set; }

    public DateTime? ExpiryAt { get; set; }

    public int? FacilityId { get; set; }

    public int? CategoryId { get; set; }

    public virtual MstAnnouncementCategory? Category { get; set; }

    public virtual MstFacility? Facility { get; set; }

    public virtual ICollection<MstAnnouncementDocument> MstAnnouncementDocuments { get; set; } = new List<MstAnnouncementDocument>();

    public virtual ICollection<MstAnnouncementReceiver> MstAnnouncementReceivers { get; set; } = new List<MstAnnouncementReceiver>();
}
