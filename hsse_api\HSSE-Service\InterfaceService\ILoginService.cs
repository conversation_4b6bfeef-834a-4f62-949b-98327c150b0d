﻿using HSSE_ModelDto.ModelDto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface ILoginService
    {
        Task<ApiResponseDto<object>> LoginAsync(LoginRequestDto loginDto);
        Task<ApiResponseDto<object>> GetUserFacilityAsync(int userId);
        Task<ApiResponseDto<object>> GetUserByFacilityIdAsync(int facilityId);
    }
}
