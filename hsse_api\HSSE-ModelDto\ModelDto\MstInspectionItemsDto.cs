﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_ModelDto.ModelDto
{
    public class MstInspectionItemDto
    {
        public int ItemId { get; set; }
        public int InspectionId { get; set; }
        public string? Description { get; set; }
        public string? SpecificLocation { get; set; }
        public string? Recommendation { get; set; }
        public string? ActionPartyName { get; set; }
        public string? Status { get; set; }
        public string? Rectification { get; set; }
        public string? AfterImagePath { get; set; }
        public DateTime? CompletionDateTime { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public string? RecommendationMediaUrl { get; set; }
        public string? Observation { get; set; }
        public string? ObservationMediaUrl { get; set; }
        public string? ObservationAttachment { get; set; }
        public string? RecommendationAttachment { get; set; }
        public VerificationStatus Verification { get; set; }
    }
}
