﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstEvents
    {
        [Key]
        public int EventID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string MediaURL { get; set; }
        public DateTime? EventDateTime { get; set; }
        public string Location { get; set; }
        public string ExternalLink { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? FacilityID { get; set; }
        public bool IsActive { get; set; }
        public DateTime? ScheduleAt { get; set; }
        public DateTime? ExpiryAt { get; set; }
        public bool IsRsvp { get; set; }
    }
}
