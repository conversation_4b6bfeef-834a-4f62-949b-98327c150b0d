﻿using HSSE_API.Helper;
using HSSE_API.NLog;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using System.Text.RegularExpressions;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class PostsController : ControllerBase
    {
        private ILog _logger;
        private string? controllerName;
        private IConfiguration _configuration;
        private readonly IPostsService _service;
        private readonly IApiLoggerService _loggerService;

        private string? actionName;
        public PostsController(IApiLoggerService loggerService, IConfiguration configuration, ILog logger, IPostsService service)
        {
            _logger = logger;
            _service = service;
            _configuration = configuration;
            _loggerService = loggerService;
        }

        /// <summary>
        /// Creates a new post
        /// </summary>
        /// <param name="postDto">Post data</param>
        /// <returns>Created post</returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> CreatePost(MstPostDto postDto)
        {
            var url = Request.GetDisplayUrl();
            var now = DateTime.UtcNow;

            _logger.Information($"[CreatePost] Request received - UserId: {postDto.UserId}, Title: {postDto.Title}");
            await _loggerService.LogInfo(new LogsDto
            {
                CreatedOn = now,
                Level = LogLevel.Information.ToString(),
                Message = $"[CreatePost] Request received - UserId: {postDto.UserId}, Title: {postDto.Title}",
                Url = url,
                StackTrace = Environment.StackTrace
            });

            try
            {
                // ✅ Handle file saving from Base64
                if (!string.IsNullOrEmpty(postDto.ImageBase64) && !string.IsNullOrEmpty(postDto.FileName))
                {
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "post-files");

                    if (!Directory.Exists(uploadFolder))
                        Directory.CreateDirectory(uploadFolder);

                    string uniqueFileName = $"{Guid.NewGuid()}_{postDto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    string base64Data = postDto.ImageBase64.Contains(",")
                        ? postDto.ImageBase64.Substring(postDto.ImageBase64.IndexOf(",") + 1)
                        : postDto.ImageBase64;

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    // Save virtual path or DB path if needed
                    postDto.ImageBase64 = $"/ExternalFiles/post-files/{uniqueFileName}";
                }
                var result = await _service.InsertOrUpdatePostAsync(postDto);

                if (result.Success)
                {
                    var successMessage = $"[CreatePost] Post created successfully - PostId: {result.Data?.PostId}";
                    _logger.Information(successMessage);
                    await _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = now,
                        Level = LogLevel.Information.ToString(),
                        Message = successMessage,
                        Url = url,
                        StackTrace = Environment.StackTrace
                    });

                    return StatusCode(result.Status, result);
                }
                else
                {
                    var failMessage = $"[CreatePost] Post creation failed - Message: {result.Message}";
                    _logger.Warning(failMessage);
                    await _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = now,
                        Level = LogLevel.Warning.ToString(),
                        Message = failMessage,
                        Url = url,
                        StackTrace = Environment.StackTrace
                    });

                    return StatusCode(result.Status, result);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"[CreatePost] Exception occurred - {ex.Message}";
                _logger.Error(errorMessage);
                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = now,
                    Level = LogLevel.Error.ToString(),
                    Message = errorMessage,
                    Exception = ex.ToString(),
                    Url = url,
                    StackTrace = ex.StackTrace,
                    Logger = nameof(CreatePost)
                });

                var error = ApiResponseDto<MstPostDto>.ErrorResponse("An unexpected error occurred.", StatusCodes.Status500InternalServerError);
                return StatusCode(StatusCodes.Status500InternalServerError, error);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetPosts(int? userId, bool? showOnlyMine)
        {
            var logTime = DateTime.Now;
            var stackTrace = Environment.StackTrace;
            var url = Request.GetDisplayUrl();

            try
            {
                _logger.Information($"[GetPostById] API called - UserId: {(userId.HasValue ? userId.ToString() : "null")}");

                await _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = logTime,
                    Level = LogLevel.Information.ToString(),
                    Message = "GetPostById API Started",
                    Url = url,
                    StackTrace = stackTrace,
                    Logger = nameof(GetPosts)
                });

                var result = await _service.GetPostsAsync(userId, showOnlyMine);

                _logger.Information($"[GetPostById] API completed - Status: {result.Status}");

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetPostById] Exception: {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.Now,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in GetPostById - {ex.Message}",
                    Exception = ex.ToString(),
                    Url = url,
                    StackTrace = ex.StackTrace ?? stackTrace,
                    Logger = nameof(GetPosts)
                });

                var errorResponse = ApiResponseDto<object>.ErrorResponse("Internal Server Error", StatusCodes.Status500InternalServerError);
                return StatusCode(StatusCodes.Status500InternalServerError, errorResponse);
            }
        }


        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> ToggleLikeAsync([FromBody] MstLikesConfigDto postDto)
        {
            var url = Request.GetDisplayUrl();
            var now = DateTime.UtcNow;
            var loggerName = nameof(ToggleLikeAsync);

            _logger.Information($"[{loggerName}] Request received - PostId: {postDto.PostId}, UserId: {postDto.UserId}");

            await _loggerService.LogInfo(new LogsDto
            {
                CreatedOn = now,
                Level = LogLevel.Information.ToString(),
                Message = $"[{loggerName}] Request received - PostId: {postDto.PostId}, UserId: {postDto.UserId}",
                Url = url,
                StackTrace = Environment.StackTrace,
                Logger = loggerName
            });

            try
            {
                var result = await _service.InsertLikestoPostAsync(postDto);

                if (result.Status == StatusCodes.Status200OK)
                {
                    _logger.Information($"[{loggerName}] Like toggled successfully for PostId: {postDto.PostId}, UserId: {postDto.UserId}");

                    await _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = now,
                        Level = LogLevel.Information.ToString(),
                        Message = $"[{loggerName}] Like toggled successfully for PostId: {postDto.PostId}, UserId: {postDto.UserId}",
                        Url = url,
                        StackTrace = Environment.StackTrace,
                        Logger = loggerName
                    });

                    return StatusCode(result.Status, result); // Returning success response
                }
                else
                {
                    _logger.Warning($"[{loggerName}] Failed to toggle like - PostId: {postDto.PostId}, UserId: {postDto.UserId}");

                    await _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = now,
                        Level = LogLevel.Warning.ToString(),
                        Message = $"[{loggerName}] Failed to toggle like - PostId: {postDto.PostId}, UserId: {postDto.UserId}",
                        Url = url,
                        StackTrace = Environment.StackTrace,
                        Logger = loggerName
                    });

                    return StatusCode(result.Status, result); // Returning error response
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception occurred - {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = now,
                    Level = LogLevel.Error.ToString(),
                    Message = $"[{loggerName}] Exception occurred - {ex.Message}",
                    Exception = ex.ToString(),
                    Url = url,
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                var errorResponse = ApiResponseDto<dynamic>.ErrorResponse("An unexpected error occurred.", StatusCodes.Status500InternalServerError);
                return StatusCode(StatusCodes.Status500InternalServerError, errorResponse);
            }
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdatePostCategory([FromBody] MstPostCategoryDto dto)
        {
            var result = await _service.InsertOrUpdatePostCategoryAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPostCategories()
        {
            var result = await _service.GetPostCategoriesAsync();
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetPostCategoryById(int id)
        {
            var result = await _service.GetPostCategoryByIdAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpDelete]
        public async Task<IActionResult> DeletePostCategory(int id)
        {
            var result = await _service.DeletePostCategoryAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetPostDetailsById(int id)
        {
            var result = await _service.GetPostDetailsByIdAsync(id);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetPostDetails()
        {
            var result = await _service.GetPostDetailsAsync();
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdatePostComment([FromBody] MstPostCommentDto dto)
        {
            var result = await _service.InsertOrUpdatePostCommentAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetPostCommentByPostId(int postId)
        {
            var result = await _service.GetPostCommentAsync(postId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateFollowupPost([FromBody] MstFollowupPostDto dto)
        {
            var result = await _service.InsertOrUpdateFollowupPostAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetAssignedPosts(int? userId, int? status = null)
        {
            var logTime = DateTime.Now;
            var stackTrace = Environment.StackTrace;
            var url = Request.GetDisplayUrl();

            try
            {
                _logger.Information($"[GetAssignedPosts] API called - UserId: {(userId.HasValue ? userId.ToString() : "null")}");

                await _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = logTime,
                    Level = LogLevel.Information.ToString(),
                    Message = "GetAssignedPosts API Started",
                    Url = url,
                    StackTrace = stackTrace,
                    Logger = nameof(GetAssignedPosts)
                });

                var result = await _service.GetAssignedPostsAsync(userId, status);

                _logger.Information($"[GetAssignedPosts] API completed - Status: {result.Status}");

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetAssignedPosts] Exception: {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.Now,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in GetAssignedPosts - {ex.Message}",
                    Exception = ex.ToString(),
                    Url = url,
                    StackTrace = ex.StackTrace ?? stackTrace,
                    Logger = nameof(GetPosts)
                });

                var errorResponse = ApiResponseDto<object>.ErrorResponse("Internal Server Error", StatusCodes.Status500InternalServerError);
                return StatusCode(StatusCodes.Status500InternalServerError, errorResponse);
            }
        }
        [HttpPost]
        public async Task<IActionResult> UpdateFollowUpStatus([FromBody] FollowupStatusUpdateRequestDto dto)
        {
            if (dto.AfterMediaUrl != null && dto.AfterMediaUrl.Any())
            {
                List<string> savedFilePaths = new List<string>();
                string externalRootPath = @"C:\HSSE-Announcements";
                string uploadFolder = Path.Combine(externalRootPath, "post-files");

                if (!Directory.Exists(uploadFolder))
                {
                    Directory.CreateDirectory(uploadFolder);
                }

                foreach (var base64File in dto.AfterMediaUrl)
                {
                    try
                    {
                        string base64Data = base64File;
                        string fileExtension = "png"; // default fallback

                        if (base64File.Contains(","))
                        {
                            var header = base64File.Substring(0, base64File.IndexOf(","));
                            base64Data = base64File.Substring(base64File.IndexOf(",") + 1);

                            var match = Regex.Match(header, @"data:(.+?);base64");
                            if (match.Success)
                            {
                                var mimeType = match.Groups[1].Value;
                                fileExtension = MimeHelper.GetExtensionFromMimeType(mimeType); // implement this helper
                            }
                        }

                        string uniqueFileName = $"{Guid.NewGuid()}.{fileExtension}";
                        string fullPath = Path.Combine(uploadFolder, uniqueFileName);
                        byte[] fileBytes = Convert.FromBase64String(base64Data);

                        await System.IO.File.WriteAllBytesAsync(fullPath, fileBytes);

                        // Add the relative path to return to the frontend or store in DB
                        string publicUrl = $"/ExternalFiles/post-files/{uniqueFileName}";
                        savedFilePaths.Add(publicUrl);
                    }
                    catch (Exception ex)
                    {
                        // Handle/log error if needed
                        Console.WriteLine($"File save failed: {ex.Message}");
                    }
                }

                // Replace base64 data with saved URLs
                dto.AfterMediaUrl = savedFilePaths;
            }
            var result = await _service.UpdateFollowupStatusAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetAssignedUser(int postId)
        {
            var result = await _service.GetAssignedUserAsync(postId);
            return StatusCode(result.Status, result);
        }
        [HttpDelete]
        public async Task<IActionResult> DeletePost(int postId, int deletedBy)
        {
            var result = await _service.DeletePostByIdAsync(postId, deletedBy);
            return StatusCode(result.Status, result);
        }
    }
}
