using System;
using System.Collections.Generic;

namespace HSSE_ModelDto.ModelDto
{
    public class MstEventDto
    {
        public int EventId { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public string? MediaUrl { get; set; }
        public DateTime? EventDateTime { get; set; }
        public string? Location { get; set; }
        public string? ExternalLink { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? FacilityId { get; set; }
        public int? LikeCount { get; set; }
        public string? FileName { get; set; } // Needed to save file with original name
        public bool IsActive { get; set; }
        public List<int>? GroupId { get; set; }
        public DateTime? ScheduleAt { get; set; }
        public DateTime? ExpiryAt { get; set; }
        public bool IsRsvp { get; set; }
        public virtual List<MstEventResponseDto>? MstEventResponses { get; set; }
        public virtual List<MstLikesConfigDto>? MstLikesConfigs { get; set; }
    }
} 