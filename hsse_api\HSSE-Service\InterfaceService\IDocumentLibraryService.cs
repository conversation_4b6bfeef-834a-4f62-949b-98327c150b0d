using HSSE_ModelDto.ModelDto;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IDocumentLibraryService
    {
        Task<ApiResponseDto<MstDocumentLibraryDto>> InsertOrUpdateDocumentAsync(MstDocumentLibraryDto dto);
        Task<ApiResponseDto<List<DocumentHierarchyDto>>> GetDocumentsAsync();
        Task<ApiResponseDto<List<DocumentHierarchyDto>>> GetDocumentsByUserIdAsync(int userId);
        Task<ApiResponseDto<MstDocumentLibraryDto>> GetDocumentByIdAsync(int documentId);
        Task<ApiResponseDto<object>> DeleteDocumentAsync(int documentId, int deletedBy);
    }
} 