﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstNewsletter
    {
        [Key]
        public int NewsletterID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string ThumbnailPath { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? FacilityID { get; set; }
        public bool IsActive { get; set; }
        public DateTime? ScheduleAt { get; set; }
    }
}
