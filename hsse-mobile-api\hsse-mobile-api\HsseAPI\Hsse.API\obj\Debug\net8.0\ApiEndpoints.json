[{"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "CreateCartOrder", "RelativePath": "api/v{version}/Cart/CreateCartOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartOrder", "Type": "Hsse.Data.Dto.Request.CartOrderRequestDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "GetAllCartDetails", "RelativePath": "api/v{version}/Cart/GetAllCartDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "locationId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "GetCartDetailsByCartId", "RelativePath": "api/v{version}/Cart/GetCartDetailsByCartId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "GetCartDetailsByUserId", "RelativePath": "api/v{version}/Cart/GetCartDetailsByUserId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "locationId", "Type": "System.Int32", "IsRequired": false}, {"Name": "facilityCode", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "RemoveCartOrder", "RelativePath": "api/v{version}/Cart/RemoveCartOrder", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "RemoveItemsFromCart", "RelativePath": "api/v{version}/Cart/RemoveItemsFromCart", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartItemId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.CartController", "Method": "UpdateItemsByCart", "RelativePath": "api/v{version}/Cart/UpdateItemsByCart", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Hsse.Data.Dto.Request.UpdateCartProductDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.LoginController", "Method": "Logout", "RelativePath": "api/v{version}/Login/LogoutUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.LoginController", "Method": "ValidateUser", "RelativePath": "api/v{version}/Login/ValidateUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeEmail", "Type": "System.String", "IsRequired": false}, {"Name": "employeeId", "Type": "System.String", "IsRequired": false}, {"Name": "fCMToken", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.MasterController", "Method": "GetAllBlocks", "RelativePath": "api/v{version}/Master/GetAllBlocks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityCode", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.MasterController", "Method": "GetAllCategories", "RelativePath": "api/v{version}/Master/GetAllCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityCode", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.MasterController", "Method": "GetAllLevelsByBlockId", "RelativePath": "api/v{version}/Master/GetAllLevelsByBlockId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "blockId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.MasterController", "Method": "GetAllLocationsByLevelId", "RelativePath": "api/v{version}/Master/GetAllLocationsByLevelId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "levelId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.MasterController", "Method": "GetAllProductsByCategoryId", "RelativePath": "api/v{version}/Master/GetAllProductsByCategoryId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryIds", "Type": "System.String", "IsRequired": false}, {"Name": "facilityCode", "Type": "System.String", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.OrderController", "Method": "CompleteOrder", "RelativePath": "api/v{version}/Order/CompleteOrderRequests", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Hsse.Data.Dto.Request.CompleteOrderRequestDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.OrderController", "Method": "CreateOrderRequest", "RelativePath": "api/v{version}/Order/CreateOrderRequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Hsse.Data.Dto.Request.OrderRequestDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.OrderController", "Method": "GetAllOrderDetails", "RelativePath": "api/v{version}/Order/GetAllOrderDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "facilityCode", "Type": "System.String", "IsRequired": false}, {"Name": "locationIds", "Type": "System.String", "IsRequired": false}, {"Name": "year", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "month", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.OrderController", "Method": "GetItemsByOrderId", "RelativePath": "api/v{version}/Order/GetItemsByOrderId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.OrderController", "Method": "UpdateOrderRequest", "RelativePath": "api/v{version}/Order/UpdateOrderRequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Hsse.Data.Dto.Request.UpdateOrderRequestDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}]