using HSSE_ModelDto.ModelDto;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IInspectionService
    {
        Task<ApiResponseDto<MstActionPartyDto>> CreateOrUpdateActionPartyAsync(MstActionPartyDto dto);
        Task<ApiResponseDto<object>> GetActionPartyByIdAsync(int actionPartyId);
        Task<ApiResponseDto<List<MstActionPartyDto>>> GetActionPartiesByFacilityIdAsync(int facilityId);
        Task<ApiResponseDto<List<ActionPartyViewDto>>> GetActionPartiesByUserIdAsync(int userId);
        Task<ApiResponseDto<object>> ToggleActionPartiesActivationAsync(int actionPartyId);
        Task<ApiResponseDto<MstInspectionDto>> CreateOrUpdateInspectionAsync(MstInspectionDto dto);
        Task<ApiResponseDto<MstInspectionDto>> GetInspectionByIdAsync(int inspectionId);
        Task<ApiResponseDto<List<MstInspectionDto>>> GetInspectionsByActionPartyAsync(int actionPartyId, int? status);
        Task<ApiResponseDto<MstInspectionItemDto>> UpdateInspectionItemStatusAsync(UpdateInspectionStatusDto req);
        Task<ApiResponseDto<List<MstInspectionCategoryDto>>> GetInspectionCategoryAsync();
        Task<ApiResponseDto<List<MstInspectionDto>>> GetUserInspectionsAsync(int userId);
    }

} 