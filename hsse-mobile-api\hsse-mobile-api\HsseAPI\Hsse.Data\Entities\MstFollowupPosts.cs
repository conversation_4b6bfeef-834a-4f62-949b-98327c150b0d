﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstFollowupPosts
    {
        [Key]
        public int FollowupID { get; set; }
        public int PostID { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string Format { get; set; }
        public string AssignedTo { get; set; }
        public DateTime? AssignedTime { get; set; }
        public int? FollowedupBy { get; set; }
        public DateTime? FollowedupTime { get; set; }
        public int? CompletedBy { get; set; }
        public DateTime? CompletedTime { get; set; }
        public int? ArchivedBy { get; set; }
        public DateTime? ArchivedTime { get; set; }
        public string Comments { get; set; }
    }
}
