﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class CreatePostDto
    {
        public int PostId { get; set; }
        public int UserId { get; set; }
        public int? FacilityId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? PostType { get; set; }
        public string? Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public bool RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public string? MediaUrl { get; set; }
        public string? ImageBase64 { get; set; }
        public string? FileName { get; set; }
        public List<int>? AssignedTo { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Priority { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class UpdatePostDto
    {
        public int PostId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public bool RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public string? MediaUrl { get; set; }
        public string? ImageBase64 { get; set; }
        public string? FileName { get; set; }
        public List<int>? AssignedTo { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Priority { get; set; }
        public int ModifiedBy { get; set; }
    }

    public class PostCategoryDto
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public int? CreatedBy { get; set; }
        public int? ModifiedBy { get; set; }
    }

    public class PostCommentDto
    {
        public int CommentId { get; set; }
        public int PostId { get; set; }
        public int UserId { get; set; }
        public string Comment { get; set; } = string.Empty;
        public int? ParentCommentId { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class FollowupPostDto
    {
        public int FollowupId { get; set; }
        public int PostId { get; set; }
        public int AssignedTo { get; set; }
        public int AssignedBy { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Priority { get; set; }
        public string? Status { get; set; }
        public string? Comments { get; set; }
        public List<string>? BeforeMediaUrl { get; set; }
        public List<string>? AfterMediaUrl { get; set; }
        public DateTime? CompletedAt { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class FollowupStatusUpdateDto
    {
        public int FollowupId { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Comments { get; set; }
        public List<string>? AfterMediaUrl { get; set; }
        public int UpdatedBy { get; set; }
    }
}
