﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstActionParty
    {
        [Key]
        public int ActionPartyId { get; set; }
        public string Name { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? ModifiedBy { get; set; }
        public bool IsActive { get; set; }
        public int? FacilityID { get; set; }
        public string Observation { get; set; }
        public string ObservationMediaUrl { get; set; }
        public string RecommendationMediaUrl { get; set; }
    }
}
