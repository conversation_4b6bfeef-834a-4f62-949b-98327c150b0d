<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>HSSE_API</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="C:\Users\<USER>\.nuget\packages\nlog.config\4.7.15\contentFiles\any\any\NLog.config" />
    <None Remove="C:\Users\<USER>\.nuget\packages\nlog.schema\5.4.0\contentFiles\any\any\NLog.xsd" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="3.1.32" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NLog" Version="5.4.0" />
    <PackageReference Include="NLog.Schema" Version="5.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HSSE-Domain\HSSE-Domain.csproj" />
    <ProjectReference Include="..\HSSE-ModelDto\HSSE-ModelDto.csproj" />
    <ProjectReference Include="..\HSSE-Service\HSSE-Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
