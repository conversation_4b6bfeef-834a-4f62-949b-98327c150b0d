﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstActionPartyUserMapping
{
    public int MappingId { get; set; }

    public int? ActionPartyId { get; set; }

    public int? UserId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual MstActionParty? ActionParty { get; set; }

    public virtual MstUser? User { get; set; }
}
