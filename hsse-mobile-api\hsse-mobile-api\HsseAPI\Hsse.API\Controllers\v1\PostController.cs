﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Text.RegularExpressions;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class PostController : ControllerBase
    {
        private readonly IPostService _postService;
        private readonly ILogger<PostController> _logger;
        private readonly IConfiguration _configuration;

        public PostController(IPostService postService, IConfiguration configuration, ILogger<PostController> logger)
        {
            _logger = logger;
            _postService = postService;
            _configuration = configuration;
        }

        /// <summary>
        /// Creates a new post
        /// </summary>
        /// <param name="postDto">Post data</param>
        /// <returns>Created post ID</returns>
        [HttpPost("CreatePost")]
        [AllowAnonymous]
        public async Task<IActionResult> CreatePost([FromBody] CreatePostDto postDto)
        {
            try
            {
                _logger.LogInformation("CreatePost request received - UserId: {UserId}, Title: {Title}", postDto.UserId, postDto.Title);

                // Handle file saving from Base64 if provided
                if (!string.IsNullOrEmpty(postDto.ImageBase64) && !string.IsNullOrEmpty(postDto.FileName))
                {
                    var savedFilePath = await SaveBase64FileAsync(postDto.ImageBase64, postDto.FileName, "post-files");
                    postDto.MediaUrl = savedFilePath;
                    postDto.ImageBase64 = null; // Clear base64 data after saving
                }

                var result = await _postService.CreatePostAsync(postDto);

                _logger.LogInformation("CreatePost completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreatePost");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Updates an existing post
        /// </summary>
        /// <param name="postDto">Updated post data</param>
        /// <returns>Update result</returns>
        [HttpPut("UpdatePost")]
        public async Task<IActionResult> UpdatePost([FromBody] UpdatePostDto postDto)
        {
            try
            {
                _logger.LogInformation("UpdatePost request received - PostId: {PostId}", postDto.PostId);

                // Handle file saving from Base64 if provided
                if (!string.IsNullOrEmpty(postDto.ImageBase64) && !string.IsNullOrEmpty(postDto.FileName))
                {
                    var savedFilePath = await SaveBase64FileAsync(postDto.ImageBase64, postDto.FileName, "post-files");
                    postDto.MediaUrl = savedFilePath;
                    postDto.ImageBase64 = null; // Clear base64 data after saving
                }

                var result = await _postService.UpdatePostAsync(postDto);

                _logger.LogInformation("UpdatePost completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdatePost");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Deletes a post
        /// </summary>
        /// <param name="postId">Post ID to delete</param>
        /// <param name="deletedBy">User ID who is deleting the post</param>
        /// <returns>Delete result</returns>
        [HttpDelete("DeletePost")]
        public async Task<IActionResult> DeletePost(int postId, int deletedBy)
        {
            try
            {
                _logger.LogInformation("DeletePost request received - PostId: {PostId}, DeletedBy: {DeletedBy}", postId, deletedBy);

                var result = await _postService.DeletePostAsync(postId, deletedBy);

                _logger.LogInformation("DeletePost completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DeletePost");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Gets a post by ID
        /// </summary>
        /// <param name="postId">Post ID</param>
        /// <param name="currentUserId">Current user ID for like status</param>
        /// <returns>Post details</returns>
        [HttpGet("GetPostById")]
        public async Task<IActionResult> GetPostById(int postId, int? currentUserId = null)
        {
            try
            {
                _logger.LogInformation("GetPostById request received - PostId: {PostId}", postId);

                var result = await _postService.GetPostByIdAsync(postId, currentUserId);

                _logger.LogInformation("GetPostById completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPostById");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Gets posts with pagination and filtering
        /// </summary>
        /// <param name="userId">User ID for filtering</param>
        /// <param name="showOnlyMine">Show only user's posts</param>
        /// <param name="facilityId">Facility ID for filtering</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>List of posts</returns>
        [HttpGet("GetPosts")]
        public async Task<IActionResult> GetPosts(int? userId = null, bool? showOnlyMine = null, int? facilityId = null, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("GetPosts request received - UserId: {UserId}, ShowOnlyMine: {ShowOnlyMine}", userId, showOnlyMine);

                var result = await _postService.GetPostsAsync(userId, showOnlyMine, facilityId, pageNumber, pageSize);

                _logger.LogInformation("GetPosts completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPosts");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Gets assigned posts for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="status">Status filter</param>
        /// <returns>List of assigned posts</returns>
        [HttpGet("GetAssignedPosts")]
        public async Task<IActionResult> GetAssignedPosts(int? userId, int? status = null)
        {
            try
            {
                _logger.LogInformation("GetAssignedPosts request received - UserId: {UserId}, Status: {Status}", userId, status);

                var result = await _postService.GetAssignedPostsAsync(userId, status);

                _logger.LogInformation("GetAssignedPosts completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAssignedPosts");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Toggles like on a post
        /// </summary>
        /// <param name="postId">Post ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Toggle result</returns>
        [HttpPost("ToggleLike")]
        [AllowAnonymous]
        public async Task<IActionResult> ToggleLike(int postId, int userId)
        {
            try
            {
                _logger.LogInformation("ToggleLike request received - PostId: {PostId}, UserId: {UserId}", postId, userId);

                var result = await _postService.ToggleLikeAsync(postId, userId);

                _logger.LogInformation("ToggleLike completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ToggleLike");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Creates or updates a post category
        /// </summary>
        /// <param name="categoryDto">Category data</param>
        /// <returns>Category ID</returns>
        [HttpPost("CreateOrUpdatePostCategory")]
        public async Task<IActionResult> CreateOrUpdatePostCategory([FromBody] PostCategoryDto categoryDto)
        {
            try
            {
                _logger.LogInformation("CreateOrUpdatePostCategory request received - CategoryName: {CategoryName}", categoryDto.CategoryName);

                var result = await _postService.CreateOrUpdatePostCategoryAsync(categoryDto);

                _logger.LogInformation("CreateOrUpdatePostCategory completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateOrUpdatePostCategory");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Gets all post categories
        /// </summary>
        /// <returns>List of categories</returns>
        [HttpGet("GetPostCategories")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPostCategories()
        {
            try
            {
                _logger.LogInformation("GetPostCategories request received");

                var result = await _postService.GetPostCategoriesAsync();

                _logger.LogInformation("GetPostCategories completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPostCategories");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Creates or updates a post comment
        /// </summary>
        /// <param name="commentDto">Comment data</param>
        /// <returns>Comment ID</returns>
        [HttpPost("CreateOrUpdatePostComment")]
        public async Task<IActionResult> CreateOrUpdatePostComment([FromBody] PostCommentDto commentDto)
        {
            try
            {
                _logger.LogInformation("CreateOrUpdatePostComment request received - PostId: {PostId}", commentDto.PostId);

                var result = await _postService.CreateOrUpdatePostCommentAsync(commentDto);

                _logger.LogInformation("CreateOrUpdatePostComment completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateOrUpdatePostComment");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Gets comments for a post
        /// </summary>
        /// <param name="postId">Post ID</param>
        /// <returns>List of comments</returns>
        [HttpGet("GetPostComments")]
        public async Task<IActionResult> GetPostComments(int postId)
        {
            try
            {
                _logger.LogInformation("GetPostComments request received - PostId: {PostId}", postId);

                var result = await _postService.GetPostCommentsAsync(postId);

                _logger.LogInformation("GetPostComments completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPostComments");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Creates or updates a followup post
        /// </summary>
        /// <param name="followupDto">Followup data</param>
        /// <returns>Followup ID</returns>
        [HttpPost("CreateOrUpdateFollowupPost")]
        public async Task<IActionResult> CreateOrUpdateFollowupPost([FromBody] FollowupPostDto followupDto)
        {
            try
            {
                _logger.LogInformation("CreateOrUpdateFollowupPost request received - PostId: {PostId}", followupDto.PostId);

                var result = await _postService.CreateOrUpdateFollowupPostAsync(followupDto);

                _logger.LogInformation("CreateOrUpdateFollowupPost completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateOrUpdateFollowupPost");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }

        /// <summary>
        /// Updates followup status
        /// </summary>
        /// <param name="statusUpdateDto">Status update data</param>
        /// <returns>Update result</returns>
        [HttpPost("UpdateFollowupStatus")]
        public async Task<IActionResult> UpdateFollowupStatus([FromBody] FollowupStatusUpdateDto statusUpdateDto)
        {
            try
            {
                _logger.LogInformation("UpdateFollowupStatus request received - FollowupId: {FollowupId}", statusUpdateDto.FollowupId);

                // Handle file saving from Base64 if provided
                if (statusUpdateDto.AfterMediaUrl?.Any() == true)
                {
                    var savedFilePaths = new List<string>();
                    foreach (var base64File in statusUpdateDto.AfterMediaUrl)
                    {
                        if (!string.IsNullOrEmpty(base64File) && base64File.Contains("base64"))
                        {
                            var savedFilePath = await SaveBase64FileAsync(base64File, null, "followup-files");
                            savedFilePaths.Add(savedFilePath);
                        }
                        else
                        {
                            savedFilePaths.Add(base64File); // Already a URL
                        }
                    }
                    statusUpdateDto.AfterMediaUrl = savedFilePaths;
                }

                var result = await _postService.UpdateFollowupStatusAsync(statusUpdateDto);

                _logger.LogInformation("UpdateFollowupStatus completed - Status: {Status}", result.Status);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateFollowupStatus");
                return StatusCode(500, new { success = false, message = "An unexpected error occurred.", status = 500 });
            }
        }
