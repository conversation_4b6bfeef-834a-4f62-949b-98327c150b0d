# Ignore Visual Studio temporary files, build results, and
# files generated by popular Visual Studio add-ons.

# User-specific files
.vscode/
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (Mono Auto Generated)
mono_crash.*

# Mono auto generated files
*.pidb
*.svd

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# IIS
_IISExpress/
.vs/

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# Project specific
**/HPBS.Core/bin/
**/HPBS.Core/obj/
**/HPBS.Data/bin/
**/HPBS.Data/obj/
**/HPBS_UAT/bin/
**/HPBS_UAT/obj/

# ASP.NET temporary files
*.Publish.xml
App_Data/

# NuGet
*.nupkg
# The packages folder can be ignored because of the new restore feature that automatically downloads missing packages.
**/packages/*
# except build/, which is used as an MSBuild target.
!**/packages/build/

# Microsoft Azure
.sonarlint/
.fusebox/

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Mac System files
.DS_Store
._*

# Hidden directories for the JetBrains Rider IDE
.idea/

# Rider project files
*.sln.iml

# If you have any database scripts or config files that are environment-specific, you can add them here:
# Example:
# **/appsettings.Development.json
# **/appsettings.Production.json

# End of .gitignore
