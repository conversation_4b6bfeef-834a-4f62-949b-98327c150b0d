{"Version": 1, "WorkspaceRootPath": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.infrastructure\\irepositories\\ipostrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|solutionrelative:hsse.infrastructure\\irepositories\\ipostrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.infrastructure\\repositories\\postrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|solutionrelative:hsse.infrastructure\\repositories\\postrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.infrastructure\\repositories\\loginrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|solutionrelative:hsse.infrastructure\\repositories\\loginrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.infrastructure\\irepositories\\iloginrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|Hsse.Infrastructure\\Hsse.Infrastructure.csproj|solutionrelative:hsse.infrastructure\\irepositories\\iloginrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|Hsse.Application\\Hsse.Application.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.application\\services\\loginservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|Hsse.Application\\Hsse.Application.csproj|solutionrelative:hsse.application\\services\\loginservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|Hsse.Application\\Hsse.Application.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.application\\services\\postservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|Hsse.Application\\Hsse.Application.csproj|solutionrelative:hsse.application\\services\\postservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|Hsse.Application\\Hsse.Application.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.application\\iservices\\ipostservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|Hsse.Application\\Hsse.Application.csproj|solutionrelative:hsse.application\\iservices\\ipostservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|Hsse.API\\Hsse.API.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.api\\controllers\\v1\\postcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|Hsse.API\\Hsse.API.csproj|solutionrelative:hsse.api\\controllers\\v1\\postcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|Hsse.API\\Hsse.API.csproj|d:\\uems\\hsse\\hsse-mobile-api\\hsse-mobile-api\\hsseapi\\hsse.api\\controllers\\v1\\logincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|Hsse.API\\Hsse.API.csproj|solutionrelative:hsse.api\\controllers\\v1\\logincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "IPostRepository.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\IRepositories\\IPostRepository.cs", "RelativeDocumentMoniker": "Hsse.Infrastructure\\IRepositories\\IPostRepository.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\IRepositories\\IPostRepository.cs", "RelativeToolTip": "Hsse.Infrastructure\\IRepositories\\IPostRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:33:38.348Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PostRepository.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Repositories\\PostRepository.cs", "RelativeDocumentMoniker": "Hsse.Infrastructure\\Repositories\\PostRepository.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Repositories\\PostRepository.cs", "RelativeToolTip": "Hsse.Infrastructure\\Repositories\\PostRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:33:23.41Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LoginRepository.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Repositories\\LoginRepository.cs", "RelativeDocumentMoniker": "Hsse.Infrastructure\\Repositories\\LoginRepository.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Repositories\\LoginRepository.cs", "RelativeToolTip": "Hsse.Infrastructure\\Repositories\\LoginRepository.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAB4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:33:10.858Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "LoginService.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Application\\Services\\LoginService.cs", "RelativeDocumentMoniker": "Hsse.Application\\Services\\LoginService.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Application\\Services\\LoginService.cs", "RelativeToolTip": "Hsse.Application\\Services\\LoginService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:32:50.423Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ILoginRepository.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\IRepositories\\ILoginRepository.cs", "RelativeDocumentMoniker": "Hsse.Infrastructure\\IRepositories\\ILoginRepository.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Infrastructure\\IRepositories\\ILoginRepository.cs", "RelativeToolTip": "Hsse.Infrastructure\\IRepositories\\ILoginRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:32:43.566Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "PostService.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Application\\Services\\PostService.cs", "RelativeDocumentMoniker": "Hsse.Application\\Services\\PostService.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Application\\Services\\PostService.cs", "RelativeToolTip": "Hsse.Application\\Services\\PostService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:32:26.545Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "IPostService.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Application\\IServices\\IPostService.cs", "RelativeDocumentMoniker": "Hsse.Application\\IServices\\IPostService.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.Application\\IServices\\IPostService.cs", "RelativeToolTip": "Hsse.Application\\IServices\\IPostService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:32:15.642Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "PostController.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.API\\Controllers\\v1\\PostController.cs", "RelativeDocumentMoniker": "Hsse.API\\Controllers\\v1\\PostController.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.API\\Controllers\\v1\\PostController.cs", "RelativeToolTip": "Hsse.API\\Controllers\\v1\\PostController.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:31:08.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "LoginController.cs", "DocumentMoniker": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.API\\Controllers\\v1\\LoginController.cs", "RelativeDocumentMoniker": "Hsse.API\\Controllers\\v1\\LoginController.cs", "ToolTip": "D:\\UEMS\\HSSE\\hsse-mobile-api\\hsse-mobile-api\\HsseAPI\\Hsse.API\\Controllers\\v1\\LoginController.cs", "RelativeToolTip": "Hsse.API\\Controllers\\v1\\LoginController.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAABsAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T04:30:01.118Z", "EditorCaption": ""}]}]}]}