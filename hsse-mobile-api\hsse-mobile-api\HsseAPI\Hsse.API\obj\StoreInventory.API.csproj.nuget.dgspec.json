{"format": 1, "restore": {"D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.API\\Hsse.API.csproj": {}}, "projects": {"D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.API\\Hsse.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.API\\Hsse.API.csproj", "projectName": "Hsse.API", "projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.API\\Hsse.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Application\\Hsse.Application.csproj": {"projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Application\\Hsse.Application.csproj"}, "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj": {"projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Asp.Versioning.Mvc": {"target": "Package", "version": "[8.1.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Application\\Hsse.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Application\\Hsse.Application.csproj", "projectName": "Hsse.Application", "projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Application\\Hsse.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj": {"projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj"}, "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Hsse.Infrastructure.csproj": {"projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Hsse.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj", "projectName": "Hsse.Data", "projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Hsse.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Hsse.Infrastructure.csproj", "projectName": "Hsse.Infrastructure", "projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Infrastructure\\Hsse.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj": {"projectPath": "D:\\UEMS\\Documents\\store-inventory-mobile-api\\HsseAPI\\Hsse.Data\\Hsse.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.0-preview3.24332.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}