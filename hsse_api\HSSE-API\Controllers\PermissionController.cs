using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class PermissionController : ControllerBase
    {
        private readonly IPermissionService _service;

        public PermissionController(IPermissionService service)
        {
            _service = service;
        }

        [HttpGet]
        public async Task<IActionResult> GetPermissionsByUserId(int roleId)
        {
            var result = await _service.GetPermissionsByUserIdAsync(roleId);
            return StatusCode(result.Status, result);
        }
    }
} 