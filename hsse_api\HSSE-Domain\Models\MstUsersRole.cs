﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstUsersRole
{
    public int RoleId { get; set; }

    public string RoleName { get; set; } = null!;

    public bool? SeededRole { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual ICollection<MstRoleMenuPermission> MstRoleMenuPermissions { get; set; } = new List<MstRoleMenuPermission>();

    public virtual ICollection<MstUserRolesConfig> MstUserRolesConfigs { get; set; } = new List<MstUserRolesConfig>();
}
