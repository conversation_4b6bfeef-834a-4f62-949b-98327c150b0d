﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.IRepositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class PostService : IPostService
    {
        private readonly IPostRepository _postRepository;
        private readonly ILogger<PostService> _logger;

        public PostService(IPostRepository postRepository, ILogger<PostService> logger)
        {
            _postRepository = postRepository;
            _logger = logger;
        }

        public async Task<ApiResponseDto<int>> CreatePostAsync(CreatePostDto postDto)
        {
            try
            {
                _logger.LogInformation("Creating post with title: {Title}", postDto.Title);

                var postId = await _postRepository.CreatePostAsync(postDto);

                _logger.LogInformation("Post created successfully with ID: {PostId}", postId);
                return ApiResponseDto<int>.SuccessResponse("Post created successfully", 201, postId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating post");
                return ApiResponseDto<int>.ErrorResponse("Failed to create post", 500);
            }
        }

        public async Task<ApiResponseDto<bool>> UpdatePostAsync(UpdatePostDto postDto)
        {
            try
            {
                _logger.LogInformation("Updating post with ID: {PostId}", postDto.PostId);

                var result = await _postRepository.UpdatePostAsync(postDto);

                if (result)
                {
                    _logger.LogInformation("Post updated successfully with ID: {PostId}", postDto.PostId);
                    return ApiResponseDto<bool>.SuccessResponse("Post updated successfully", 200, true);
                }
                else
                {
                    _logger.LogWarning("Post not found or update failed for ID: {PostId}", postDto.PostId);
                    return ApiResponseDto<bool>.ErrorResponse("Post not found or update failed", 404);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating post with ID: {PostId}", postDto.PostId);
                return ApiResponseDto<bool>.ErrorResponse("Failed to update post", 500);
            }
        }

        public async Task<ApiResponseDto<bool>> DeletePostAsync(int postId, int deletedBy)
        {
            try
            {
                _logger.LogInformation("Deleting post with ID: {PostId}", postId);

                var result = await _postRepository.DeletePostAsync(postId, deletedBy);

                if (result)
                {
                    _logger.LogInformation("Post deleted successfully with ID: {PostId}", postId);
                    return ApiResponseDto<bool>.SuccessResponse("Post deleted successfully", 200, true);
                }
                else
                {
                    _logger.LogWarning("Post not found for deletion with ID: {PostId}", postId);
                    return ApiResponseDto<bool>.ErrorResponse("Post not found", 404);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting post with ID: {PostId}", postId);
                return ApiResponseDto<bool>.ErrorResponse("Failed to delete post", 500);
            }
        }

        public async Task<ApiResponseDto<PostResponseDto>> GetPostByIdAsync(int postId, int? currentUserId = null)
        {
            try
            {
                _logger.LogInformation("Getting post with ID: {PostId}", postId);

                var post = await _postRepository.GetPostByIdAsync(postId, currentUserId);

                if (post != null)
                {
                    _logger.LogInformation("Post retrieved successfully with ID: {PostId}", postId);
                    return ApiResponseDto<PostResponseDto>.SuccessResponse("Post retrieved successfully", 200, post);
                }
                else
                {
                    _logger.LogWarning("Post not found with ID: {PostId}", postId);
                    return ApiResponseDto<PostResponseDto>.ErrorResponse("Post not found", 404);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting post with ID: {PostId}", postId);
                return ApiResponseDto<PostResponseDto>.ErrorResponse("Failed to retrieve post", 500);
            }
        }

        public async Task<ApiResponseDto<List<PostResponseDto>>> GetPostsAsync(int? userId = null, bool? showOnlyMine = null, int? facilityId = null, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting posts - UserId: {UserId}, ShowOnlyMine: {ShowOnlyMine}, FacilityId: {FacilityId}", userId, showOnlyMine, facilityId);

                var posts = await _postRepository.GetPostsAsync(userId, showOnlyMine, facilityId, pageNumber, pageSize);

                _logger.LogInformation("Retrieved {Count} posts", posts.Count);
                return ApiResponseDto<List<PostResponseDto>>.SuccessResponse("Posts retrieved successfully", 200, posts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting posts");
                return ApiResponseDto<List<PostResponseDto>>.ErrorResponse("Failed to retrieve posts", 500);
            }
        }

        public async Task<ApiResponseDto<List<PostResponseDto>>> GetAssignedPostsAsync(int? userId, int? status = null)
        {
            try
            {
                _logger.LogInformation("Getting assigned posts - UserId: {UserId}, Status: {Status}", userId, status);

                var posts = await _postRepository.GetAssignedPostsAsync(userId, status);

                _logger.LogInformation("Retrieved {Count} assigned posts", posts.Count);
                return ApiResponseDto<List<PostResponseDto>>.SuccessResponse("Assigned posts retrieved successfully", 200, posts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned posts");
                return ApiResponseDto<List<PostResponseDto>>.ErrorResponse("Failed to retrieve assigned posts", 500);
            }
        }

        public async Task<ApiResponseDto<bool>> ToggleLikeAsync(int postId, int userId)
        {
            try
            {
                _logger.LogInformation("Toggling like for post {PostId} by user {UserId}", postId, userId);

                var result = await _postRepository.ToggleLikeAsync(postId, userId);

                _logger.LogInformation("Like toggled successfully for post {PostId} by user {UserId}", postId, userId);
                return ApiResponseDto<bool>.SuccessResponse("Like toggled successfully", 200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling like for post {PostId} by user {UserId}", postId, userId);
                return ApiResponseDto<bool>.ErrorResponse("Failed to toggle like", 500);
            }
        }

        public async Task<ApiResponseDto<int>> GetLikeCountAsync(int postId)
        {
            try
            {
                var count = await _postRepository.GetLikeCountAsync(postId);
                return ApiResponseDto<int>.SuccessResponse("Like count retrieved successfully", 200, count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting like count for post {PostId}", postId);
                return ApiResponseDto<int>.ErrorResponse("Failed to get like count", 500);
            }
        }

        public async Task<ApiResponseDto<bool>> IsLikedByUserAsync(int postId, int userId)
        {
            try
            {
                var isLiked = await _postRepository.IsLikedByUserAsync(postId, userId);
                return ApiResponseDto<bool>.SuccessResponse("Like status retrieved successfully", 200, isLiked);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking like status for post {PostId} by user {UserId}", postId, userId);
                return ApiResponseDto<bool>.ErrorResponse("Failed to check like status", 500);
            }
        }

        public async Task<ApiResponseDto<int>> CreateOrUpdatePostCategoryAsync(PostCategoryDto categoryDto)
        {
            try
            {
                _logger.LogInformation("Creating/updating post category: {CategoryName}", categoryDto.CategoryName);

                var categoryId = await _postRepository.CreateOrUpdatePostCategoryAsync(categoryDto);

                _logger.LogInformation("Post category created/updated successfully with ID: {CategoryId}", categoryId);
                return ApiResponseDto<int>.SuccessResponse("Post category saved successfully", 200, categoryId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating post category");
                return ApiResponseDto<int>.ErrorResponse("Failed to save post category", 500);
            }
        }

        public async Task<ApiResponseDto<bool>> DeletePostCategoryAsync(int categoryId)
        {
            try
            {
                _logger.LogInformation("Deleting post category with ID: {CategoryId}", categoryId);

                var result = await _postRepository.DeletePostCategoryAsync(categoryId);

                if (result)
                {
                    _logger.LogInformation("Post category deleted successfully with ID: {CategoryId}", categoryId);
                    return ApiResponseDto<bool>.SuccessResponse("Post category deleted successfully", 200, true);
                }
                else
                {
                    _logger.LogWarning("Post category not found for deletion with ID: {CategoryId}", categoryId);
                    return ApiResponseDto<bool>.ErrorResponse("Post category not found", 404);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting post category with ID: {CategoryId}", categoryId);
                return ApiResponseDto<bool>.ErrorResponse("Failed to delete post category", 500);
            }
        }

        public async Task<ApiResponseDto<PostCategoryResponseDto>> GetPostCategoryByIdAsync(int categoryId)
        {
            try
            {
                var category = await _postRepository.GetPostCategoryByIdAsync(categoryId);

                if (category != null)
                {
                    return ApiResponseDto<PostCategoryResponseDto>.SuccessResponse("Post category retrieved successfully", 200, category);
                }
                else
                {
                    return ApiResponseDto<PostCategoryResponseDto>.ErrorResponse("Post category not found", 404);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting post category with ID: {CategoryId}", categoryId);
                return ApiResponseDto<PostCategoryResponseDto>.ErrorResponse("Failed to retrieve post category", 500);
            }
        }

        public async Task<ApiResponseDto<List<PostCategoryResponseDto>>> GetPostCategoriesAsync()
        {
            try
            {
                var categories = await _postRepository.GetPostCategoriesAsync();
                return ApiResponseDto<List<PostCategoryResponseDto>>.SuccessResponse("Post categories retrieved successfully", 200, categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting post categories");
                return ApiResponseDto<List<PostCategoryResponseDto>>.ErrorResponse("Failed to retrieve post categories", 500);
            }
        }

        public async Task<ApiResponseDto<int>> CreateOrUpdatePostCommentAsync(PostCommentDto commentDto)
        {
            try
            {
                _logger.LogInformation("Creating/updating post comment for post {PostId}", commentDto.PostId);

                var commentId = await _postRepository.CreateOrUpdatePostCommentAsync(commentDto);

                _logger.LogInformation("Post comment created/updated successfully with ID: {CommentId}", commentId);
                return ApiResponseDto<int>.SuccessResponse("Comment saved successfully", 200, commentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating post comment");
                return ApiResponseDto<int>.ErrorResponse("Failed to save comment", 500);
            }
        }

        public async Task<ApiResponseDto<List<PostCommentResponseDto>>> GetPostCommentsAsync(int postId)
        {
            try
            {
                var comments = await _postRepository.GetPostCommentsAsync(postId);
                return ApiResponseDto<List<PostCommentResponseDto>>.SuccessResponse("Comments retrieved successfully", 200, comments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting comments for post {PostId}", postId);
                return ApiResponseDto<List<PostCommentResponseDto>>.ErrorResponse("Failed to retrieve comments", 500);
            }
        }

        public async Task<ApiResponseDto<int>> CreateOrUpdateFollowupPostAsync(FollowupPostDto followupDto)
        {
            try
            {
                _logger.LogInformation("Creating/updating followup for post {PostId}", followupDto.PostId);

                var followupId = await _postRepository.CreateOrUpdateFollowupPostAsync(followupDto);

                _logger.LogInformation("Followup created/updated successfully with ID: {FollowupId}", followupId);
                return ApiResponseDto<int>.SuccessResponse("Followup saved successfully", 200, followupId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating followup");
                return ApiResponseDto<int>.ErrorResponse("Failed to save followup", 500);
            }
        }

        public async Task<ApiResponseDto<bool>> UpdateFollowupStatusAsync(FollowupStatusUpdateDto statusUpdateDto)
        {
            try
            {
                _logger.LogInformation("Updating followup status for followup {FollowupId}", statusUpdateDto.FollowupId);

                var result = await _postRepository.UpdateFollowupStatusAsync(statusUpdateDto);

                if (result)
                {
                    _logger.LogInformation("Followup status updated successfully for followup {FollowupId}", statusUpdateDto.FollowupId);
                    return ApiResponseDto<bool>.SuccessResponse("Followup status updated successfully", 200, true);
                }
                else
                {
                    _logger.LogWarning("Followup not found for status update with ID: {FollowupId}", statusUpdateDto.FollowupId);
                    return ApiResponseDto<bool>.ErrorResponse("Followup not found", 404);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating followup status for followup {FollowupId}", statusUpdateDto.FollowupId);
                return ApiResponseDto<bool>.ErrorResponse("Failed to update followup status", 500);
            }
        }

        public async Task<ApiResponseDto<List<FollowupPostResponseDto>>> GetFollowupsByPostIdAsync(int postId)
        {
            try
            {
                var followups = await _postRepository.GetFollowupsByPostIdAsync(postId);
                return ApiResponseDto<List<FollowupPostResponseDto>>.SuccessResponse("Followups retrieved successfully", 200, followups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting followups for post {PostId}", postId);
                return ApiResponseDto<List<FollowupPostResponseDto>>.ErrorResponse("Failed to retrieve followups", 500);
            }
        }

        public async Task<ApiResponseDto<List<AssignedUserResponseDto>>> GetAssignedUsersAsync(int postId)
        {
            try
            {
                var users = await _postRepository.GetAssignedUsersAsync(postId);
                return ApiResponseDto<List<AssignedUserResponseDto>>.SuccessResponse("Assigned users retrieved successfully", 200, users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned users for post {PostId}", postId);
                return ApiResponseDto<List<AssignedUserResponseDto>>.ErrorResponse("Failed to retrieve assigned users", 500);
            }
        }

        public async Task<ApiResponseDto<int>> GetTotalPostCountAsync(int? facilityId = null)
        {
            try
            {
                var count = await _postRepository.GetTotalPostCountAsync(facilityId);
                return ApiResponseDto<int>.SuccessResponse("Total post count retrieved successfully", 200, count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total post count");
                return ApiResponseDto<int>.ErrorResponse("Failed to get total post count", 500);
            }
        }

        public async Task<ApiResponseDto<int>> GetActivePostCountAsync(int? facilityId = null)
        {
            try
            {
                var count = await _postRepository.GetActivePostCountAsync(facilityId);
                return ApiResponseDto<int>.SuccessResponse("Active post count retrieved successfully", 200, count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active post count");
                return ApiResponseDto<int>.ErrorResponse("Failed to get active post count", 500);
            }
        }

        public async Task<ApiResponseDto<int>> GetPendingFollowupCountAsync(int? userId = null)
        {
            try
            {
                var count = await _postRepository.GetPendingFollowupCountAsync(userId);
                return ApiResponseDto<int>.SuccessResponse("Pending followup count retrieved successfully", 200, count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending followup count");
                return ApiResponseDto<int>.ErrorResponse("Failed to get pending followup count", 500);
            }
        }
    }
}
