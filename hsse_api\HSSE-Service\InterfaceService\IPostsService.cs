﻿using HSSE_ModelDto.ModelDto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IPostsService
    {
        Task<ApiResponseDto<MstPostDto>> InsertOrUpdatePostAsync(MstPostDto postDto);
        Task<ApiResponseDto<List<MstPostDto>>> GetPostsAsync(int? userId, bool? showOnlyMine);
        Task<ApiResponseDto<dynamic>> InsertLikestoPostAsync(MstLikesConfigDto postDto);
        Task<ApiResponseDto<object>> InsertOrUpdatePostCategoryAsync(MstPostCategoryDto dto);
        Task<ApiResponseDto<object>> GetPostCategoriesAsync();
        Task<ApiResponseDto<object>> GetPostCategoryByIdAsync(int catId);
        Task<ApiResponseDto<object>> DeletePostCategoryAsync(int catId);
        Task<ApiResponseDto<MstPostDto>> GetPostDetailsByIdAsync(int postId);
        Task<ApiResponseDto<List<MstPostDto>>> GetPostDetailsAsync();
        Task<ApiResponseDto<object>> InsertOrUpdatePostCommentAsync(MstPostCommentDto dto);
        Task<ApiResponseDto<List<MstPostCommentDto>>> GetPostCommentAsync(int postId);
        Task<ApiResponseDto<object>> InsertOrUpdateFollowupPostAsync(MstFollowupPostDto dto);
        Task<ApiResponseDto<List<MstPostDto>>> GetAssignedPostsAsync(int? userId, int? status = null);
        Task<ApiResponseDto<string>> UpdateFollowupStatusAsync(FollowupStatusUpdateRequestDto request);
        Task<ApiResponseDto<object>> GetAssignedUserAsync(int postId);
        Task<ApiResponseDto<object>> DeletePostByIdAsync(int postId, int deletedBy);
    }
}
