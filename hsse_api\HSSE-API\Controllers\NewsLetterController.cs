using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;
using System.IO;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class NewsLetterController : ControllerBase
    {
        private readonly INewsLetterService _service;
        private readonly IConfiguration _configuration;

        public NewsLetterController(IConfiguration configuration, INewsLetterService service)
        {
            _service = service;
            _configuration = configuration;
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateNewsLetter([FromBody] MstNewsletterDto dto)
        {
            try
            {
                if (!string.IsNullOrEmpty(dto.ThumbnailPath) && !string.IsNullOrEmpty(dto.FileName))
                {
                    // Save in external folder on C: drive
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "newsletter-images");

                    if (!Directory.Exists(uploadFolder))
                    {
                        Directory.CreateDirectory(uploadFolder);
                    }

                    string uniqueFileName = $"{Guid.NewGuid()}_{dto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    // Strip base64 prefix if exists
                    string base64Data = dto.ThumbnailPath;
                    if (base64Data.Contains(","))
                    {
                        base64Data = base64Data.Substring(base64Data.IndexOf(",") + 1);
                    }

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    // Save virtual download path
                    dto.ThumbnailPath = $"/ExternalFiles/newsletter-images/{uniqueFileName}";
                }

                var result = await _service.InsertOrUpdateNewsLetterAsync(dto);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetNewsLettersByUserId(int userId)
        {
            var result = await _service.GetNewsLettersByUserIdAsync(userId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetNewsLettersById(int newsletterId)
        {
            var result = await _service.GetNewsLettersByIdAsync(newsletterId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetNewsLettersByFacilityId(int facilityId, int userId)
        {
            var result = await _service.GetNewsLettersByFacilityIdAsync(facilityId, userId);
            return StatusCode(result.Status, result);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteNewsLetter(int newsLetterId)
        {
            var result = await _service.DeleteNewsLetterAsync(newsLetterId);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> ToggleNewsLetterActivation(int newsLetterId)
        {
            var result = await _service.ToggleNewsLetterActivationAsync(newsLetterId);
            return StatusCode(result.Status, result);
        }
    }
} 