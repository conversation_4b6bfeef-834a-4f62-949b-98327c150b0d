﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_ModelDto.ModelDto
{
    public class MstInspectionDto
    {
        public int InspectionId { get; set; }
        public int? FacilityId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public DateTime? InspectionDate { get; set; }
        public string? AfterImagePath { get; set; }
        public string? ReferenceNo { get; set; }
        public int? TypeOfInspection { get; set; }
        public string? TypeOfInspectionName { get; set; }
        public int? ActionPartyId { get; set; }
        public int? DepartmentId { get; set; }
        public string? SpecificLocation { get; set; }
        public string? InspectorName { get; set; }
        public int? Verification { get; set; }
        public string? Rectification { get; set; }

        public int? CreatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public DateTime? CompletionDateTime { get; set; }
        public string? Observation { get; set; }
        public string? Recommendation { get; set; }
        public int? Status { get; set; }

        public string? ObservationAttachmentBase64 { get; set; }
        public string? ObservationAttachmentName { get; set; }
        public int? ContactPersonId { get; set; }
        public string? ContactPersonName { get; set; }
        public string? RecommendationAttachmentBase64 { get; set; }
        public string? RecommendationAttachmentName { get; set; }
    }
    public class UpdateInspectionStatusDto
    {
        public int InspectionId { get; set; }
        public int Status { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? Remarks { get; set; }
        public bool isVerified { get; set; }
        public string? AfterImageUrl { get; set; }
        public string? AfterImageName { get; set; }
        public int? ChangedBy { get; set; }

    }
}
