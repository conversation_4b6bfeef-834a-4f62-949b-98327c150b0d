{"format": 1, "restore": {"D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-API\\HSSE-API.csproj": {}}, "projects": {"D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-API\\HSSE-API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-API\\HSSE-API.csproj", "projectName": "HSSE-API", "projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-API\\HSSE-API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj": {"projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj"}, "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj": {"projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj"}, "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Service\\HSSE-Service.csproj": {"projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Service\\HSSE-Service.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "NLog": {"target": "Package", "version": "[5.4.0, )"}, "NLog.Schema": {"target": "Package", "version": "[5.4.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj", "projectName": "HSSE-Domain", "projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "NLog.Config": {"target": "Package", "version": "[4.7.15, )"}, "NLog.Schema": {"target": "Package", "version": "[5.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj", "projectName": "HSSE-ModelDto", "projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "NLog.Config": {"target": "Package", "version": "[4.7.15, )"}, "NLog.Schema": {"target": "Package", "version": "[5.4.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Service\\HSSE-Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Service\\HSSE-Service.csproj", "projectName": "HSSE-Service", "projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Service\\HSSE-Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj": {"projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-Domain\\HSSE-Domain.csproj"}, "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj": {"projectPath": "D:\\UEMS\\Documents\\CopyProject\\hsse_api\\HSSE-ModelDto\\HSSE-ModelDto.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "NLog.Config": {"target": "Package", "version": "[4.7.15, )"}, "NLog.Schema": {"target": "Package", "version": "[5.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}