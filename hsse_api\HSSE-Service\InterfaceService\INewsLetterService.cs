using HSSE_ModelDto.ModelDto;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface INewsLetterService
    {
        Task<ApiResponseDto<object>> InsertOrUpdateNewsLetterAsync(MstNewsletterDto newsLetterDto);
        Task<ApiResponseDto<object>> GetNewsLettersByUserIdAsync(int userId);
        Task<ApiResponseDto<object>> GetNewsLettersByFacilityIdAsync(int facilityId, int userId);
        Task<ApiResponseDto<object>> DeleteNewsLetterAsync(int newsLetterId);
        Task<ApiResponseDto<object>> ToggleNewsLetterActivationAsync(int newsLetterId);
        Task<ApiResponseDto<object>> GetNewsLettersByIdAsync(int newsletterId);
    }
} 