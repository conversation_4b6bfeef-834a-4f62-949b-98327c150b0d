[{"ContainingType": "Hsse.API.Controllers.v1.LoginController", "Method": "Logout", "RelativePath": "api/v{version}/Login/LogoutUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Int32", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.LoginController", "Method": "ValidateUser", "RelativePath": "api/v{version}/Login/ValidateUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeEmail", "Type": "System.String", "IsRequired": false}, {"Name": "employeeId", "Type": "System.String", "IsRequired": false}, {"Name": "fCMToken", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.PostController", "Method": "CreateOrUpdateLikes", "RelativePath": "api/v{version}/Post/CreateOrUpdateLikes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createLikeDto", "Type": "Hsse.Data.Dto.Request.CreateLikeDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.PostController", "Method": "CreatePost", "RelativePath": "api/v{version}/Post/CreatePost", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createPostDto", "Type": "Hsse.Data.Dto.Request.CreatePostDto", "IsRequired": true}, {"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.PostController", "Method": "GetPostCategories", "RelativePath": "api/v{version}/Post/GetPostCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Hsse.API.Controllers.v1.PostController", "Method": "GetPosts", "RelativePath": "api/v{version}/Post/GetPosts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "", "IsRequired": true}], "ReturnTypes": []}]