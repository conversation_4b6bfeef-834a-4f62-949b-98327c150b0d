﻿using NLog;
using ILogger = NLog.ILogger;

namespace HSSE_API.NLog
{
    public class LogNLog : ILog
    {
        private static ILogger logger = LogManager.GetCurrentClassLogger();
        public LogNLog()
        {

        }

        public void Information(string message)
        {

            logger.Info(message);

        }

        public void Warning(string message)
        {

            logger.Warn(message);

        }

        public void Debug(string message)
        {

            logger.Debug(message);

        }

        public void Error(string message)
        {
            logger.Error(message);

        }
    }
}
