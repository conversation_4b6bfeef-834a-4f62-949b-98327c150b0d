﻿using HSSE_ModelDto.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_ModelDto.ModelDto
{
    public class FollowupStatusUpdateRequestDto
    {
        public int FollowupId { get; set; }

        public int PostId { get; set; }

        public PostStatus Status { get; set; }
        public List<string>? AfterMediaUrl { get; set; }
        public int UserId { get; set; }  // This is the user performing the action
        public string? CompletedComments { get; set; }

    }
}
