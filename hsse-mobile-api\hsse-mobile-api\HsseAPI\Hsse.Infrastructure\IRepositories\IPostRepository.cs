﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IPostRepository
    {
        // Post CRUD operations
        Task<int> CreatePostAsync(CreatePostDto postDto);
        Task<bool> UpdatePostAsync(UpdatePostDto postDto);
        Task<bool> DeletePostAsync(int postId, int deletedBy);
        Task<PostResponseDto?> GetPostByIdAsync(int postId, int? currentUserId = null);
        Task<List<PostResponseDto>> GetPostsAsync(int? userId = null, bool? showOnlyMine = null, int? facilityId = null, int pageNumber = 1, int pageSize = 10);
        Task<List<PostResponseDto>> GetAssignedPostsAsync(int? userId, int? status = null);

        // Post Categories
        Task<int> CreateOrUpdatePostCategoryAsync(PostCategoryDto categoryDto);
        Task<bool> DeletePostCategoryAsync(int categoryId);
        Task<PostCategoryResponseDto?> GetPostCategoryByIdAsync(int categoryId);
        Task<List<PostCategoryResponseDto>> GetPostCategoriesAsync();

        // Post Comments
        Task<int> CreateOrUpdatePostCommentAsync(PostCommentDto commentDto);
        Task<List<PostCommentResponseDto>> GetPostCommentsAsync(int postId);

        // Post Likes
        Task<bool> ToggleLikeAsync(int postId, int userId);
        Task<int> GetLikeCountAsync(int postId);
        Task<bool> IsLikedByUserAsync(int postId, int userId);

        // Followup Posts
        Task<int> CreateOrUpdateFollowupPostAsync(FollowupPostDto followupDto);
        Task<bool> UpdateFollowupStatusAsync(FollowupStatusUpdateDto statusUpdateDto);
        Task<List<FollowupPostResponseDto>> GetFollowupsByPostIdAsync(int postId);
        Task<List<AssignedUserResponseDto>> GetAssignedUsersAsync(int postId);

        // Statistics
        Task<int> GetTotalPostCountAsync(int? facilityId = null);
        Task<int> GetActivePostCountAsync(int? facilityId = null);
        Task<int> GetPendingFollowupCountAsync(int? userId = null);
    }
}
