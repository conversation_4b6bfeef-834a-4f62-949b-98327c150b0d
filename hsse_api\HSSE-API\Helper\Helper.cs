﻿namespace HSSE_API.Helper
{
    public static class MimeHelper
    {
        public static string GetExtensionFromMimeType(string mimeType)
        {
            return mimeType switch
            {
                "application/pdf" => "pdf",
                "image/jpeg" => "jpg",
                "image/png" => "png",
                "image/gif" => "gif",
                "image/bmp" => "bmp",
                "image/webp" => "webp",
                "text/plain" => "txt",
                "application/json" => "json",
                "text/csv" => "csv",
                "application/zip" => "zip",
                "application/x-7z-compressed" => "7z",
                "application/x-rar-compressed" => "rar",
                "application/msword" => "doc",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => "docx",
                "application/vnd.ms-excel" => "xls",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => "xlsx",
                "application/vnd.ms-powerpoint" => "ppt",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation" => "pptx",
                "application/xml" => "xml",
                "text/html" => "html",
                "application/javascript" => "js",
                "application/octet-stream" => "bin",
                _ => "bin"
            };
        }

    }
}
