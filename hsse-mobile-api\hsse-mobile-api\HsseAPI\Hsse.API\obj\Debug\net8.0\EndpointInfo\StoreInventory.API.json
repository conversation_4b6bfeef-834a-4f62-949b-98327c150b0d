{"openapi": "3.0.1", "info": {"title": "Hsse API", "description": "API for managing store inventory", "version": "v1"}, "paths": {"/api/v{version}/Cart/CreateCartOrder": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartOrderRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartOrderRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CartOrderRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Cart/RemoveItemsFromCart": {"delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "cartItemId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Cart/UpdateItemsByCart": {"put": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCartProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCartProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCartProductDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Cart/GetCartDetailsByCartId": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "cartId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Cart/GetAllCartDetails": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "locationId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Cart/RemoveCartOrder": {"delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "cartId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Cart/GetCartDetailsByUserId": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "locationId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "facilityCode", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Login/ValidateUser": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "employeeEmail", "in": "query", "schema": {"type": "string"}}, {"name": "employeeId", "in": "query", "schema": {"type": "string"}}, {"name": "fCMToken", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Login/LogoutUser": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Master/GetAllBlocks": {"get": {"tags": ["Master"], "parameters": [{"name": "facilityCode", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Master/GetAllLevelsByBlockId": {"get": {"tags": ["Master"], "parameters": [{"name": "blockId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Master/GetAllLocationsByLevelId": {"get": {"tags": ["Master"], "parameters": [{"name": "levelId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Master/GetAllCategories": {"get": {"tags": ["Master"], "parameters": [{"name": "facilityCode", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Master/GetAllProductsByCategoryId": {"get": {"tags": ["Master"], "parameters": [{"name": "categoryIds", "in": "query", "schema": {"type": "string"}}, {"name": "facilityCode", "in": "query", "schema": {"type": "string"}}, {"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Order/CreateOrderRequest": {"post": {"tags": ["Order"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Order/UpdateOrderRequest": {"post": {"tags": ["Order"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Order/GetAllOrderDetails": {"get": {"tags": ["Order"], "parameters": [{"name": "facilityCode", "in": "query", "schema": {"type": "string"}}, {"name": "locationIds", "in": "query", "schema": {"type": "string"}}, {"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Order/GetItemsByOrderId": {"get": {"tags": ["Order"], "parameters": [{"name": "orderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v{version}/Order/CompleteOrderRequests": {"post": {"tags": ["Order"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteOrderRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteOrderRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteOrderRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"CartOrderRequestDto": {"type": "object", "properties": {"locationId": {"type": "integer", "format": "int32"}, "facilityCode": {"type": "string", "nullable": true}, "cartItems": {"type": "array", "items": {"$ref": "#/components/schemas/CartProductsDto"}, "nullable": true}}, "additionalProperties": false}, "CartProductsDto": {"type": "object", "properties": {"productId": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32"}, "requestedQuantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CompleteOrderRequestDto": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32"}, "acknowledgedBy": {"type": "string", "nullable": true}, "ackSignature": {"type": "string", "nullable": true}, "ackRemarks": {"type": "string", "nullable": true}, "facilityCode": {"type": "string", "nullable": true}, "deliveredItems": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveredItemDto"}, "nullable": true}}, "additionalProperties": false}, "DeliveredItemDto": {"type": "object", "properties": {"orderItemId": {"type": "integer", "format": "int32"}, "deliveredQuantity": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderItemDto": {"type": "object", "properties": {"productId": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32"}, "requestedQuantity": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderRequestDto": {"type": "object", "properties": {"locationId": {"type": "integer", "format": "int32"}, "facilityCode": {"type": "string", "nullable": true}, "requestItems": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateCartProductDto": {"type": "object", "properties": {"cartItemId": {"type": "integer", "format": "int32"}, "requestedQuantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateOrderRequestDto": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32"}, "requestItems": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemDto"}, "nullable": true}, "rejectItems": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Enter 'Bearer' [space] and then your token in the text input below.\n\nExample: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}