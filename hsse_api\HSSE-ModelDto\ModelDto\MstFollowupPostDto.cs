﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_ModelDto.ModelDto
{
    public class MstFollowupPostDto
    {
        public int FollowupId { get; set; }

        public int PostId { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? CreatedDate { get; set; }

        public string? Format { get; set; }

        public List<int>? AssignedTo { get; set; }

        public DateTime? AssignedTime { get; set; }

        public int? FollowedupBy { get; set; }

        public DateTime? FollowedupTime { get; set; }

        public int? CompletedBy { get; set; }

        public DateTime? CompletedTime { get; set; }

        public int? ArchivedBy { get; set; }

        public DateTime? ArchivedTime { get; set; }
    }
}
