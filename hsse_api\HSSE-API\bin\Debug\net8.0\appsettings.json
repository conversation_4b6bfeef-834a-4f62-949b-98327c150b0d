{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Logs": {
    "InternalLogPath": "C:\\HSSELogs",
    "ApplicationLogPath": "C:\\HSSELogs\\NLog.txt",
    "LogtoDatabase": "TRUE",
    "LogLevel": 0 // 0- <PERSON> (Logs everything), 1- Debug, 2- Info, 3- Warning, 4- <PERSON>rror, 5- <PERSON><PERSON>, 6- OFF
  },
  "ConnectionStrings": {
    "dbconn": "Server=************;Initial Catalog=HSSE_DB_Latest;User Id=sa;Password=**$q2023P@s;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true;Integrated Security=false;"
  },
  "ApiUrls": {
    "LoginApi": "https://projects.sustainedgeconsulting.com/UEMS/EntryPassSSOApi/api/user/login"
  },
  "Jwt": {
    "Issuer": "Issuer",
    "Audience": "Audience",
    "Key": "bd1a1ccf8095037f361a4d351e7c0de65f0776bfc2f478ea8d312c763bb6caca",
    "ExpirationHours": 24
  },
  "AllowedHosts": "*"
}
