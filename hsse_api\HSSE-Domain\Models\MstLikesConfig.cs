﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstLikesConfig
{
    public int LikeId { get; set; }

    public int? PostId { get; set; }

    public int? EventId { get; set; }

    public int UserId { get; set; }

    public DateTime? LikedAt { get; set; }

    public bool IsLiked { get; set; }

    public virtual MstEvent? Event { get; set; }

    public virtual MstPost? Post { get; set; }

    public virtual MstUser User { get; set; } = null!;
}
