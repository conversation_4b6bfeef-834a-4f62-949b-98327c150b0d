﻿using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.NLog
{
    public interface ILoggerManager
    {
        void Information(string message);
        void Warning(string message);
        void Debug(string message);
        void Error(string message);
    }

    public class LoggerManager : ILoggerManager
    {
        private static readonly ILogger logger = LogManager.GetCurrentClassLogger();

        public void Information(string message)
        {

            logger.Info(message);

        }

        public void Warning(string message)
        {

            logger.Warn(message);

        }

        public void Debug(string message)
        {

            logger.Debug(message);

        }

        public void Error(string message)
        {
            logger.Error(message);

        }
    }
}
