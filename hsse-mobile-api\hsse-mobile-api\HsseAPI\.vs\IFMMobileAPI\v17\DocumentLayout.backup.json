{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\StoreInventory.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\storeinventory.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\StoreInventory.API.csproj|solutionrelative:ifmmobile.api\\storeinventory.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\storeinventory.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\storeinventory.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\storeinventory.data.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\storeinventory.data.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\StoreInventory.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\storeinventory.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\StoreInventory.Application.csproj|solutionrelative:ifmmobile.application\\storeinventory.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\repositories\\storagerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\repositories\\storagerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\irepositories\\inotificationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\irepositories\\inotificationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\irepositories\\imasterrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\irepositories\\imasterrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\response\\userworkorderrequestresdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\response\\userworkorderrequestresdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\irepositories\\ifaultreportingrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\irepositories\\ifaultreportingrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\helper\\masterdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\helper\\masterdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\helper\\ifmdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\helper\\ifmdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\helper\\emailhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\helper\\emailhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\helper\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\helper\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\entities\\mstgeneralsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\entities\\mstgeneralsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\entities\\dtwoimage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\entities\\dtwoimage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\entities\\dtppmimage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\entities\\dtppmimage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\ackworequestreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\ackworequestreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\acknowledgeppmrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\acknowledgeppmrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\services\\faultreportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|solutionrelative:ifmmobile.application\\services\\faultreportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\services\\ppmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|solutionrelative:ifmmobile.application\\services\\ppmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\StoreInventory.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\services\\masterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\StoreInventory.Application.csproj|solutionrelative:ifmmobile.application\\services\\masterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\iservices\\ippmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|solutionrelative:ifmmobile.application\\iservices\\ippmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\InventoryMobile.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\iservices\\imasterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\InventoryMobile.Application.csproj|solutionrelative:ifmmobile.application\\iservices\\imasterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\iservices\\ifaultreportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|solutionrelative:ifmmobile.application\\iservices\\ifaultreportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.application\\helper\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29D86778-F021-4535-91A2-74EAB1776EDA}|IFMMobile.Application\\IFMMobile.Application.csproj|solutionrelative:ifmmobile.application\\helper\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\repositories\\faultreportingrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\repositories\\faultreportingrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|solutionrelative:ifmmobile.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\controllers\\v1\\ppmcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|solutionrelative:ifmmobile.api\\controllers\\v1\\ppmcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\controllers\\v1\\mastercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|solutionrelative:ifmmobile.api\\controllers\\v1\\mastercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\woimagedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\woimagedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\validateuserrequestdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\validateuserrequestdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\updatewostatusreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\updatewostatusreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\updateworeasonreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\updateworeasonreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\updatereasondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\updatereasondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\updateppmrequestbystatusdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\updateppmrequestbystatusdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\updateppmreasondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\updateppmreasondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\InventoryMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\saveworkorderreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\InventoryMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\saveworkorderreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\ppmimagedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\ppmimagedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\notificationdetailsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\StoreInventory.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\notificationdetailsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\InventoryMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\addorremovewoimagereqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\InventoryMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\addorremovewoimagereqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\request\\addorremoveppmreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\request\\addorremoveppmreqdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\controllers\\v1\\faultreportingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|solutionrelative:ifmmobile.api\\controllers\\v1\\faultreportingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\InventoryMobile.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\controllers\\v1\\baseapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\InventoryMobile.API.csproj|solutionrelative:ifmmobile.api\\controllers\\v1\\baseapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\repositories\\notificationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\repositories\\notificationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{0CBDAB25-E8CB-4334-974F-B6E95474FA5A}|IFMMobile.API\\IFMMobile.API.csproj|solutionrelative:ifmmobile.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\repositories\\ppmrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\repositories\\ppmrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\irepositories\\istoragerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\irepositories\\istoragerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\irepositories\\ippmrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\irepositories\\ippmrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.infrastructure\\repositories\\masterrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4014331E-47FC-42B7-BC3C-FF08E9484223}|IFMMobile.Infrastructure\\IFMMobile.Infrastructure.csproj|solutionrelative:ifmmobile.infrastructure\\repositories\\masterrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\response\\responsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\response\\responsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|c:\\users\\<USER>\\desktop\\codebase\\sustainedge\\inventory-api\\ifmmobileapi\\ifmmobile.data\\dto\\response\\ppmresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2CF399E-FBF2-47D6-879F-35044E7D853F}|IFMMobile.Data\\IFMMobile.Data.csproj|solutionrelative:ifmmobile.data\\dto\\response\\ppmresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 19, "Title": "PPMService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Services\\PPMService.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\Services\\PPMService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Services\\PPMService.cs", "RelativeToolTip": "IFMMobile.Application\\Services\\PPMService.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAYwBoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T05:12:42.218Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "PPMRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\PPMRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\Repositories\\PPMRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\PPMRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\Repositories\\PPMRepository.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAcwFMAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T05:11:46.712Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "IPPMService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\IServices\\IPPMService.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\IServices\\IPPMService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\IServices\\IPPMService.cs", "RelativeToolTip": "IFMMobile.Application\\IServices\\IPPMService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T05:13:04.772Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "INotificationRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\INotificationRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\IRepositories\\INotificationRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\INotificationRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\IRepositories\\INotificationRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:12:44.507Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "StorageRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\StorageRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\Repositories\\StorageRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\StorageRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\Repositories\\StorageRepository.cs", "ViewState": "AgIAAA8AAAAAAAAAAAASwB4AAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T05:42:56.81Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "IFMMobile.API", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\StoreInventory.API.csproj", "RelativeDocumentMoniker": "IFMMobile.API\\StoreInventory.API.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\StoreInventory.API.csproj", "RelativeToolTip": "IFMMobile.API\\StoreInventory.API.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-05T06:27:40.826Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IFMMobile.Application", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\StoreInventory.Application.csproj", "RelativeDocumentMoniker": "IFMMobile.Application\\StoreInventory.Application.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\StoreInventory.Application.csproj", "RelativeToolTip": "IFMMobile.Application\\StoreInventory.Application.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-05T06:29:17.935Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "StoreInventory.Data.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\StoreInventory.Data.csproj", "RelativeDocumentMoniker": "IFMMobile.Data\\StoreInventory.Data.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\StoreInventory.Data.csproj", "RelativeToolTip": "IFMMobile.Data\\StoreInventory.Data.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-05T06:32:26.261Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "IFMMobile.Infrastructure", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj", "RelativeToolTip": "IFMMobile.Infrastructure\\StoreInventory.Infrastructure.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-05T06:31:33.681Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "IMasterRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IMasterRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\IRepositories\\IMasterRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IMasterRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\IRepositories\\IMasterRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:13:46.629Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "UserWorkOrderRequestResDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Response\\UserWorkOrderRequestResDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Response\\UserWorkOrderRequestResDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Response\\UserWorkOrderRequestResDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Response\\UserWorkOrderRequestResDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:15:35.955Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "IFaultReportingRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IFaultReportingRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\IRepositories\\IFaultReportingRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IFaultReportingRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\IRepositories\\IFaultReportingRepository.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAuwA0AAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T09:12:30.585Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "MasterDBContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\MasterDBContext.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Helper\\MasterDBContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\MasterDBContext.cs", "RelativeToolTip": "IFMMobile.Data\\Helper\\MasterDBContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:15:00.268Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "EmailHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\EmailHelper.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Helper\\EmailHelper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\EmailHelper.cs", "RelativeToolTip": "IFMMobile.Data\\Helper\\EmailHelper.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAxwAkAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:14:44.751Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\DependencyInjection.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Helper\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\DependencyInjection.cs", "RelativeToolTip": "IFMMobile.Data\\Helper\\DependencyInjection.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:14:30.9Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "MstGeneralSettings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Entities\\MstGeneralSettings.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Entities\\MstGeneralSettings.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Entities\\MstGeneralSettings.cs", "RelativeToolTip": "IFMMobile.Data\\Entities\\MstGeneralSettings.cs", "ViewState": "AgIAABEAAAAAAAAAAAAgwAsAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:14:25.934Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "DtWOImage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Entities\\DtWOImage.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Entities\\DtWOImage.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Entities\\DtWOImage.cs", "RelativeToolTip": "IFMMobile.Data\\Entities\\DtWOImage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:14:19.93Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "MasterService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Services\\MasterService.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\Services\\MasterService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Services\\MasterService.cs", "RelativeToolTip": "IFMMobile.Application\\Services\\MasterService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:13:17.303Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "IFMDBContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\IFMDBContext.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Helper\\IFMDBContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Helper\\IFMDBContext.cs", "RelativeToolTip": "IFMMobile.Data\\Helper\\IFMDBContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T09:30:17.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "FaultReportingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Services\\FaultReportingService.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\Services\\FaultReportingService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Services\\FaultReportingService.cs", "RelativeToolTip": "IFMMobile.Application\\Services\\FaultReportingService.cs", "ViewState": "AgIAAGwAAAAAAAAAAAAqwGoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T10:04:00.331Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "AcknowledgePPMRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AcknowledgePPMRequest.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\AcknowledgePPMRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AcknowledgePPMRequest.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\AcknowledgePPMRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-03T10:17:01.487Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "AckWORequestReqDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AckWORequestReqDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\AckWORequestReqDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AckWORequestReqDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\AckWORequestReqDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:11:28.437Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "DtPPMImage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Entities\\DtPPMImage.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Entities\\DtPPMImage.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Entities\\DtPPMImage.cs", "RelativeToolTip": "IFMMobile.Data\\Entities\\DtPPMImage.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAuwA8AAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T09:31:34.467Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "IFaultReportingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\IServices\\IFaultReportingService.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\IServices\\IFaultReportingService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\IServices\\IFaultReportingService.cs", "RelativeToolTip": "IFMMobile.Application\\IServices\\IFaultReportingService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T10:03:25.84Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Helper\\DependencyInjection.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\Helper\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\Helper\\DependencyInjection.cs", "RelativeToolTip": "IFMMobile.Application\\Helper\\DependencyInjection.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAQwBMAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T11:57:22.927Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Program.cs", "RelativeDocumentMoniker": "IFMMobile.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Program.cs", "RelativeToolTip": "IFMMobile.API\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T09:37:34.784Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "IMasterService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\IServices\\IMasterService.cs", "RelativeDocumentMoniker": "IFMMobile.Application\\IServices\\IMasterService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Application\\IServices\\IMasterService.cs", "RelativeToolTip": "IFMMobile.Application\\IServices\\IMasterService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T06:32:59.634Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "PPMController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\PPMController.cs", "RelativeDocumentMoniker": "IFMMobile.API\\Controllers\\v1\\PPMController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\PPMController.cs", "RelativeToolTip": "IFMMobile.API\\Controllers\\v1\\PPMController.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAAwDkAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-03T11:28:14.214Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "MasterController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\MasterController.cs", "RelativeDocumentMoniker": "IFMMobile.API\\Controllers\\v1\\MasterController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\MasterController.cs", "RelativeToolTip": "IFMMobile.API\\Controllers\\v1\\MasterController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T14:48:44.46Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "WOImageDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\WOImageDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\WOImageDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\WOImageDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\WOImageDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:12:01.018Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "ValidateUserRequestDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\ValidateUserRequestDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\ValidateUserRequestDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\ValidateUserRequestDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\ValidateUserRequestDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:11:56.683Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "FaultReportingRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\FaultReportingRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\Repositories\\FaultReportingRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\FaultReportingRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\Repositories\\FaultReportingRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T12:11:33.294Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "UpdateWOReasonReqDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdateWOReasonReqDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\UpdateWOReasonReqDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdateWOReasonReqDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\UpdateWOReasonReqDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:11:15.499Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "UpdateReasonDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdateReasonDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\UpdateReasonDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdateReasonDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\UpdateReasonDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:11:42.708Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "UpdatePPMRequestByStatusDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdatePPMRequestByStatusDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\UpdatePPMRequestByStatusDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdatePPMRequestByStatusDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\UpdatePPMRequestByStatusDto.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAkAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T09:08:08.059Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "UpdatePPMReasonDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdatePPMReasonDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\UpdatePPMReasonDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdatePPMReasonDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\UpdatePPMReasonDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-04T04:58:37.215Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "UpdateWOStatusReqDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdateWOStatusReqDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\UpdateWOStatusReqDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\UpdateWOStatusReqDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\UpdateWOStatusReqDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:10:25.291Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "SaveWorkOrderReqDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\SaveWorkOrderReqDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\SaveWorkOrderReqDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\SaveWorkOrderReqDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\SaveWorkOrderReqDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T06:33:18.076Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "PPMImageDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\PPMImageDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\PPMImageDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\PPMImageDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\PPMImageDto.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAoAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T09:19:26.877Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "NotificationDetailsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\NotificationDetailsDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\NotificationDetailsDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\NotificationDetailsDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\NotificationDetailsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T07:11:35.142Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 40, "Title": "AddOrRemovePPMReqDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AddOrRemovePPMReqDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\AddOrRemovePPMReqDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AddOrRemovePPMReqDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\AddOrRemovePPMReqDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T09:14:01.678Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "AddOrRemoveWOImageReqDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AddOrRemoveWOImageReqDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Request\\AddOrRemoveWOImageReqDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Request\\AddOrRemoveWOImageReqDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Request\\AddOrRemoveWOImageReqDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T06:35:13.093Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "BaseAPIController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\BaseAPIController.cs", "RelativeDocumentMoniker": "IFMMobile.API\\Controllers\\v1\\BaseAPIController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\BaseAPIController.cs", "RelativeToolTip": "IFMMobile.API\\Controllers\\v1\\BaseAPIController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T06:32:36.555Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "FaultReportingController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\FaultReportingController.cs", "RelativeDocumentMoniker": "IFMMobile.API\\Controllers\\v1\\FaultReportingController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\Controllers\\v1\\FaultReportingController.cs", "RelativeToolTip": "IFMMobile.API\\Controllers\\v1\\FaultReportingController.cs", "ViewState": "AgIAAPkAAAAAAAAAAAAgwAcBAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T10:10:07.86Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "NotificationRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\NotificationRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\Repositories\\NotificationRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\NotificationRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\Repositories\\NotificationRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T11:10:08.542Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\appsettings.json", "RelativeDocumentMoniker": "IFMMobile.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.API\\appsettings.json", "RelativeToolTip": "IFMMobile.API\\appsettings.json", "ViewState": "AgIAAAkAAAAAAAAAAAAAACQAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-02-20T12:16:32.067Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "IStorageRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IStorageRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\IRepositories\\IStorageRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IStorageRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\IRepositories\\IStorageRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAwwAoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T07:25:50.23Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "IPPMRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IPPMRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\IRepositories\\IPPMRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\IRepositories\\IPPMRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\IRepositories\\IPPMRepository.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAgwA0AAABpAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T05:12:07.772Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\DependencyInjection.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\DependencyInjection.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\DependencyInjection.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAswAwAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T11:57:57.904Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "MasterRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\MasterRepository.cs", "RelativeDocumentMoniker": "IFMMobile.Infrastructure\\Repositories\\MasterRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Infrastructure\\Repositories\\MasterRepository.cs", "RelativeToolTip": "IFMMobile.Infrastructure\\Repositories\\MasterRepository.cs", "ViewState": "AgIAADAAAAAAAAAAAAAYwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-01T05:35:25.106Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "ResponseDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Response\\ResponseDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Response\\ResponseDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Response\\ResponseDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Response\\ResponseDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-25T09:10:36.987Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "PPMResponseDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Response\\PPMResponseDto.cs", "RelativeDocumentMoniker": "IFMMobile.Data\\Dto\\Response\\PPMResponseDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Codebase\\SUSTAINEDGE\\inventory-api\\IFMMobileAPI\\IFMMobile.Data\\Dto\\Response\\PPMResponseDto.cs", "RelativeToolTip": "IFMMobile.Data\\Dto\\Response\\PPMResponseDto.cs", "ViewState": "AgIAAAMAAAAAAAAAAIBJwAoAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T10:20:22.667Z", "EditorCaption": ""}]}]}]}