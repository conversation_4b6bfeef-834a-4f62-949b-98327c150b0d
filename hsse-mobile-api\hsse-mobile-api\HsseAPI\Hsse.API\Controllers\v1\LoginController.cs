﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.Helpers;
using Microsoft.AspNetCore.Authorization;
using Azure;

namespace Hsse.API.Controllers.v1
{
    
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class LoginController : ControllerBase
    {
        private readonly ILoginService _ILoginService;
        private readonly ILogger<LoginController> _logger;
        private readonly AuthorizeHelper _authorizeHelper;

        public LoginController(ILoginService loginService, AuthorizeHelper authorizeHelper, ILogger<LoginController> logger)
        {
            _logger = logger;
            _ILoginService = loginService;
            _authorizeHelper = authorizeHelper;

        }
    }
}
