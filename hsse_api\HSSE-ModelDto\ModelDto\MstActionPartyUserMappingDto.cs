using System;

namespace HSSE_ModelDto.ModelDto
{
    public class MstActionPartyUserMappingDto
    {
        public int MappingId { get; set; }
        public int? ActionPartyId { get; set; }
        public int? UserId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? ModifiedBy { get; set; }
        public bool IsActive { get; set; }
        // Optionally, add UserName if needed for display
        public string? UserName { get; set; }
    }
} 