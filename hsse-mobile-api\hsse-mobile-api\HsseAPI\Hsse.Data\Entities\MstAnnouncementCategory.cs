﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{

    public class MstAnnouncementCategory
    {
        [Key]
        public int AnnoucementCategoryId { get; set; }
        public string AnnoucementCategoryName { get; set; }
        public bool Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? ModifiedBy { get; set; }
    }
}
