﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.Constants
{
    public class AppMessages
    {
        public const string PostCreatedSuccessfully = "Post created successfully.";
        public const string PostLikedSuccessfully = "Post liked successfully.";
        public const string PostFetchSuccess = "Posts fetched successfully";
        public const string PostCreationFailed = "Failed to create post.";
        public const string InternalServerError = "An unexpected error occurred. Please try again later.";
        public const string PostUpdatedSuccessfully = "Post updated successfully.";
        public const string PostNotFound = "Post not found.";
        public const string GroupSaved = "Group saved successfully.";
        public const string GroupRetrieved = "Group retrieved successfully.";
        public const string GroupsRetrieved = "Groups retrieved successfully.";
        public const string GroupDeleted = "Group deleted successfully.";
        public const string GroupNotFound = "Group not found.";

        public const string GroupMemberSaved = "Group member saved successfully.";
        public const string GroupMemberRetrieved = "Group member retrieved successfully.";
        public const string GroupMembersRetrieved = "Group members retrieved successfully.";
        public const string GroupMemberDeleted = "Group member deleted successfully.";
        public const string GroupMemberNotFound = "Group member not found.";
        public const string UserListCannotBeEmpty = "User list cannot be empty.";
        public const string GroupMemberUpdated = "Group members updated successfully.";
        public const string GroupAlreadyHasMembers = "This group already has members. Please use the edit functionality.";

        public const string AnnouncementSaved = "Announcement saved successfully.";
        public const string AnnouncementRetrieved = "Announcement retrieved successfully.";
        public const string AnnouncementsRetrieved = "Announcements retrieved successfully.";
        public const string AnnouncementDeleted = "Announcement deleted successfully.";
        public const string AnnouncementNotFound = "Announcement not found.";
        public const string AnnouncementUpdated = "Announcement update successfully.";

        public const string ReceiverSaved = "Announcement receiver saved successfully.";
        public const string InvalidInput = "Invalid input data.";
        public const string ErrorOccurred = "An unexpected error occurred.";

        public const string EventResponseSaved = "Event response saved successfully.";
        public const string EventResponseUpdated = "Event response updated successfully.";
        public const string EventResponseRetrieved = "Event response(s) retrieved successfully.";
        public const string EventResponseNotFound = "Event response not found.";
        public const string LikeSaved = "Like saved successfully.";
        public const string LikeUpdated = "Like updated successfully.";
        public const string LikeRetrieved = "Like(s) retrieved successfully.";
        public const string LikeNotFound = "Like not found.";


        // Events
        public const string EventRetrieved = "Event(s) retrieved successfully.";
        public const string EventNotFound = "Event not found.";
        public const string EventCreatedSuccessfully = "Event created successfully.";
        public const string EventUpdatedSuccessfully = "Event updated successfully.";
        public const string EventActivatedSuccessfully = "Event activated successfully.";
        public const string EventDeactivatedSuccessfully = "Event deactivated successfully.";
        public const string EventAlreadyActive = "Event is already active.";
        public const string EventAlreadyInactive = "Event is already inactive.";

        public const string NewsletterCreatedSuccessfully = "Newsletter created successfully.";
        public const string NewsletterUpdatedSuccessfully = "Newsletter updated successfully.";
        public const string NewsletterRetrieved = "Newsletter(s) retrieved successfully.";
        public const string NewsletterDeletedSuccessfully = "Newsletter deleted successfully.";
        public const string NewsletterNotFound = "Newsletter not found.";
        public const string NewsletterActivatedSuccessfully = "Newsletter activated successfully.";
        public const string NewsletterDeactivatedSuccessfully = "Newsletter deactivated successfully.";

        public const string AnnouncementCategoryActivatedSuccessfully = "Announcement category activated successfully.";
        public const string AnnouncementCategoryDeactivatedSuccessfully = "Announcement category deactivated successfully.";
        public const string ActionPartiesActivateSuccess = "ActionParty activate success";
        public const string ActionPartiesDeActivateSuccess = "ActionParty deactivate";
    }
}
