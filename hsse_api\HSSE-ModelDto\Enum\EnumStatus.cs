﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_ModelDto.Enum
{
    public enum AnnouncementStatus
    {
        Draft = 0,
        Published = 1,
        Archived = 2
    }
    public enum SendToType
    {
        User,
        Group
    }
    public enum EventDateFilter
    {
        None = 0,
        //Online = 1,
        Today = 1,
        Tomorrow = 2,
        ThisWeek = 3,
        ThisWeekend = 4,
        ThisMonth = 5,
        NextMonth = 6
    }
    public enum PostStatus
    {
        Assigned = 1,
        Followup = 2,
        Completed = 3,
        Archived = 4

    }
}
