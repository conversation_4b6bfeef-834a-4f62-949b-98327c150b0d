﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{

    public class MstInspectionItemComments
    {
        [Key]
        public int InspectionCommentId { get; set; }
        public int ItemId { get; set; }
        public string CommentText { get; set; }
        public int? CommentedBy { get; set; }
        public DateTime? CommentedAt { get; set; }
    }
}
