﻿using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;

namespace HSSE_API.AutoMapper
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<MstPost, MstPostDto>().ReverseMap();
            CreateMap<MstPostDto, MstPost>().ReverseMap(); 
            CreateMap<MstLikesConfig, MstLikesConfigDto>().ReverseMap();
            CreateMap<MstPostComment, MstPostCommentDto>().ReverseMap();
            CreateMap<MstFollowupPost, MstFollowupPostDto>().ReverseMap();

            CreateMap<MstAnnouncement, MstAnnouncementDto>().ReverseMap();
            CreateMap<MstAnnouncementDto, MstAnnouncement>().ReverseMap();

            CreateMap<MstAnnouncementReceiverDto, MstAnnouncementReceiver>().ReverseMap();
            CreateMap<MstAnnouncementReceiver, MstAnnouncementReceiverDto>().ReverseMap();
            CreateMap<MstGroup, MstGroupDto>().ReverseMap();
            CreateMap<MstGroupDto, MstGroup>().ReverseMap();
            CreateMap<MstGroupMember, MstGroupMemberDto>().ReverseMap();
            CreateMap<MstGroupMemberDto, MstGroupMember>().ReverseMap();

            CreateMap<MstEvent, MstEventDto>()
                .ForMember(dest => dest.LikeCount, opt => opt.MapFrom(src => src.MstLikesConfigs != null ? src.MstLikesConfigs.Count(l => l.IsLiked) : 0))
                .ForMember(dest => dest.MstLikesConfigs, opt => opt.Ignore())
                .ForMember(dest => dest.MstEventResponses, opt => opt.MapFrom(src => src.MstEventResponses))
                .ReverseMap();
            CreateMap<MstEventResponse, MstEventResponseDto>().ReverseMap();

            CreateMap<MstNewsletter, MstNewsletterDto>().ReverseMap();

            CreateMap<MstAnnouncementCategory, MstAnnoucementCategoryDto>().ReverseMap();

            CreateMap<MstPostCategory, MstPostCategoryDto>().ReverseMap(); 

            CreateMap<MstDocumentLibrary, MstDocumentLibraryDto>().ReverseMap();

            // Inspection mappings
            CreateMap<MstActionParty, MstActionPartyDto>()
                .ForMember(dest => dest.UserMappings, opt => opt.MapFrom(src => src.MstActionPartyUserMappings))
                .ReverseMap()
                .ForMember(dest => dest.MstActionPartyUserMappings, opt => opt.MapFrom(src => src.UserMappings));
            CreateMap<MstActionPartyUserMapping, MstActionPartyUserMappingDto>().ReverseMap();

        
            CreateMap<MstInspectionItem, MstInspectionItemDto>().ReverseMap();
            CreateMap<MstInspectionCategory, MstInspectionCategoryDto>().ReverseMap();
            CreateMap<MstInspectionCategoryDto, MstInspectionCategory>().ReverseMap();

        }
    }
}
