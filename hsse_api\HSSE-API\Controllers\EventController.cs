﻿using HSSE_API.NLog;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class EventController : ControllerBase
    {
        private readonly ILog _logger;
        private readonly IConfiguration _configuration;
        private readonly IEventService _service;

        public EventController(IConfiguration configuration, ILog logger, IEventService service)
        {
            _logger = logger;
            _service = service;
            _configuration = configuration;
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateEvent([FromBody] MstEventDto dto)
        {
            _logger.Information($"[InsertOrUpdateEvent] Request: {dto}");
            try
            {
                if (!string.IsNullOrEmpty(dto.MediaUrl) && !string.IsNullOrEmpty(dto.FileName))
                {
                    // ✅ Save in external folder on C: drive
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "event-images");

                    if (!Directory.Exists(uploadFolder))
                    {
                        Directory.CreateDirectory(uploadFolder);
                    }

                    string uniqueFileName = $"{Guid.NewGuid()}_{dto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    // Strip base64 prefix if exists
                    string base64Data = dto.MediaUrl;
                    if (base64Data.Contains(","))
                    {
                        base64Data = base64Data.Substring(base64Data.IndexOf(",") + 1);
                    }

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    // Save virtual download path
                    dto.MediaUrl = $"/ExternalFiles/event-images/{uniqueFileName}";
                }

                var result = await _service.InsertOrUpdateEventAsync(dto);
                _logger.Information($"[InsertOrUpdateEvent] Success: {result.Data}");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[InsertOrUpdateEvent] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEvents(int userId)
        {
            _logger.Information("[GetEvents] Request received");
            try
            {
                var result = await _service.GetEventsAsync(userId);
                _logger.Information("[GetEvents] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetEvents] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEventById(int id)
        {
            _logger.Information($"[GetEventById] Request: EventId={id}");
            try
            {
                var result = await _service.GetEventByIdAsync(id);
                _logger.Information("[GetEventById] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetEventById] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEventsByFacilityId(
        int facilityId,
        int userId,
        int dateFilterId = 0,      // optional filter id (enum int value)
        string? search = null
    )
        {
            _logger.Information($"[GetEventsByFacilityId] Request: FacilityId={facilityId}, DateFilterId={dateFilterId}, Search={search}");
            try
            {
                var result = await _service.GetEventsByFacilityIdAsync(facilityId, userId, search, dateFilterId);
                _logger.Information("[GetEventsByFacilityId] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEventsByFacilityId] Exception - {ex.Message}");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }


        [HttpPost]
        public async Task<IActionResult> SaveEventResponse([FromBody] MstEventResponseDto dto)
        {
            _logger.Information($"[SaveEventResponse] Request: {dto}");
            try
            {
                var result = await _service.SaveEventResponseAsync(dto);
                _logger.Information($"[SaveEventResponse] Success: {result.Data}");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[SaveEventResponse] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEventResponsesByEventId(int eventId)
        {
            _logger.Information($"[GetEventResponsesByEventId] Request: EventId={eventId}");
            try
            {
                var result = await _service.GetEventResponsesByEventIdAsync(eventId);
                _logger.Information("[GetEventResponsesByEventId] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetEventResponsesByEventId] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveEventLike(int eventId, int userId)
        {
            _logger.Information($"[SaveEventLike] Request: EventId={eventId}, UserId={userId}");
            try
            {
                var result = await _service.SaveEventLikeAsync(eventId, userId);
                _logger.Information($"[SaveEventLike] Success: {result.Data}");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[SaveEventLike] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEventLikesByEventId(int eventId)
        {
            _logger.Information($"[GetEventLikesByEventId] Request: EventId={eventId}");
            try
            {
                var result = await _service.GetEventLikesByEventIdAsync(eventId);
                _logger.Information("[GetEventLikesByEventId] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetEventLikesByEventId] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> ToggleEventActivation(int eventId)
        {
            _logger.Information($"[ToggleEventActivation] Request: EventId={eventId}");
            try
            {
                var result = await _service.ToggleEventActivationAsync(eventId);
                _logger.Information("[ToggleEventActivation] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[ToggleEventActivation] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetEventRsvpDetails(int eventId)
        {
            _logger.Information($"[GetEventLikesByEventId] Request: EventId={eventId}");
            try
            {
                var result = await _service.GetEventRsvpDetailsAsync(eventId);
                _logger.Information("[GetEventLikesByEventId] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetEventLikesByEventId] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
    }
} 