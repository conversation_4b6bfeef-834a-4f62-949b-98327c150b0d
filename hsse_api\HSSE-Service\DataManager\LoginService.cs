﻿using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using HSSE_Service.NLog;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using JwtPayload = HSSE_ModelDto.ModelDto.JwtPayload;

namespace HSSE_Service.DataManager
{
    public class LoginService : ILoginService
    {
        private IMapper _mapper;
        readonly HsseDbLatestContext _context;
        private ILoggerManager _logger;
        private readonly HttpClient _httpClient;
        private IConfiguration _configuration;

        public LoginService(IConfiguration configuration, HttpClient httpClient, HsseDbLatestContext dbContext, IMapper mapper, ILoggerManager logger)
        {
            _httpClient = httpClient;
            _context = dbContext;
            _mapper = mapper;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<ApiResponseDto<object>> LoginAsync(LoginRequestDto loginDto)
        {
            try
            {
                //_logger.Information($"[SSO Login] Attempting login for: {loginDto.Username}");
                //var ssoBaseUrl = _configuration["SSOBaseUrl"];
                //var validateEndpoint = "https://projects.sustainedgeconsulting.com/UEMS/EntryPassSSOApi/api/tokenvalidation/validatetoken";
                //var fullUrl = $"{validateEndpoint}";
                //var requestBody = new
                //{
                //    requestObj = loginDto, // assuming loginDto has a `Token` property containing the JWT string
                //    project = "Entrypass"
                //};

                //var jsonBody = JsonConvert.SerializeObject(requestBody);
                //var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

                //var response = await _httpClient.PostAsync(fullUrl, content);
                //var responseString = await response.Content.ReadAsStringAsync();
                //var result = client.Execute(request);
                var ssoUrl = _configuration["ApiUrls:LoginApi"];
                var request = new HttpRequestMessage(HttpMethod.Post, ssoUrl);

                // Prepare request body
                var requestData = new
                {
                    data = new
                    {
                        facilityId = 1,
                        userLanguage = "en",
                        requestObj = new
                        {
                            password = loginDto.Password,
                            email = loginDto.Username,
                        }
                    }
                };

                // Attach serialized JSON to the request
                request.Content = new StringContent(JsonConvert.SerializeObject(requestData), Encoding.UTF8, "application/json");

                // Send request and get response
                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.Warning($"[SSO Login] Failed: {response.StatusCode}, Response: {content}");
                    return ApiResponseDto<object>.ErrorResponse($"SSO login failed. {response.ReasonPhrase}", (int)response.StatusCode);
                }

                var rawResponse = JsonConvert.DeserializeObject<LoginApiResponse>(content);
                if (rawResponse?.Obj == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Invalid SSO API response.", StatusCodes.Status500InternalServerError);
                }

                var decodedObj = JsonConvert.DeserializeObject<SsoDecodedResponse>(rawResponse.Obj);

                if (decodedObj == null || string.IsNullOrEmpty(decodedObj.Jwt))
                {
                    return ApiResponseDto<object>.ErrorResponse("Missing JWT token from response.", StatusCodes.Status500InternalServerError);
                }

                // Check if user exists
                var existingUser = await _context.MstUsers.FirstOrDefaultAsync(x => x.SsoUserId == decodedObj.UserId);

                if (existingUser == null)
                {
                    var user = new MstUser
                    {
                        Username = decodedObj.UserName,
                        FirstName = decodedObj.UserFullName ?? decodedObj.UserName ?? "",
                        LastName = "",
                        Email = decodedObj.UserEmail ?? "",
                        Password = decodedObj.UserName, // Random dummy password
                        ProfileImageUrl = decodedObj.PhotoUrl,
                        Bio = null,
                        PrimaryFacilityId = null,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = null,
                        LastLogin = DateTime.UtcNow,
                        IsActive = true,
                        SsoUserId = decodedObj.UserId,
                    };

                    _context.MstUsers.Add(user);
                    await _context.SaveChangesAsync();
                    existingUser = user;
                    var userRoleConfig = new MstUserRolesConfig
                    {
                        UserId = user.UserId,
                        RoleId = decodedObj.RoleId , // Default to RoleId = 1 if not provided
                        FacilityId = null,               // Optional: Set facility if required
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.MstUserRolesConfigs.Add(userRoleConfig);
                    await _context.SaveChangesAsync();
                }

                // Generate new JWT token using MstUser.UserId
                var newJwtToken = GenerateJwtToken(existingUser); // implement GenerateJwtToken(int userId)


                _logger.Information($"[SSO Login] Success for: {loginDto}");

                return ApiResponseDto<object>.SuccessResponse("Login successful", StatusCodes.Status200OK, new 
                {
                    Token = newJwtToken,
                    Username = decodedObj.UserName,
                    Email = decodedObj.UserEmail
                });
            }
            catch (Exception ex)
            {
                _logger.Error($"[SSO Login] Exception: {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse("An error occurred during login.", StatusCodes.Status500InternalServerError);
            }
        }

        private string GenerateJwtToken(MstUser userdetails)
        {
            var generatedUtc = DateTime.UtcNow;
            var expiryUtc = generatedUtc.AddHours(int.Parse(_configuration["Jwt:ExpirationHours"]));

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
        new Claim("ssoUserId", userdetails.UserId.ToString()),
        new Claim("userEmail", userdetails.Username ?? string.Empty),
        new Claim("userName", userdetails.Username ?? string.Empty),
        new Claim("roleId", userdetails?.ToString() ?? "0"),
        new Claim("generatedUtc", generatedUtc.ToString("o")),
        new Claim("expiryUtc", expiryUtc.ToString("o"))
    };

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: expiryUtc,
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        public async Task<ApiResponseDto<object>> GetUserFacilityAsync(int userId)
        {
            try
            {
                var facilities = await _context.MstUserRolesConfigs
                    .Include(f => f.Facility)
                    .Where(x => x.UserId == userId && x.Facility != null && x.RoleId == 1)
                    .Select(x => new 
                    {
                        FacilityId = x.Facility.FacilityId,
                        FacilityName = x.Facility.FacilityName
                    })
                    .Distinct() // optional, if duplicates exist
                    .ToListAsync();

                if (facilities == null || !facilities.Any())
                {
                    return ApiResponseDto<object>.ErrorResponse("Facilities not found", StatusCodes.Status404NotFound);
                }

                return ApiResponseDto<object>.SuccessResponse("Success", StatusCodes.Status200OK, facilities);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetUserByFacilityIdAsync(int facilityId)
        {
            try
            {
                var facilities = await _context.MstUserRolesConfigs
                    .Include(f => f.User)
                    .Where(x => x.FacilityId == facilityId && x.Facility != null)
                    .Select(x => new
                    {
                        UserId = x.User.UserId,
                        UserName = x.User.Username
                    })
                    .Distinct() // optional, if duplicates exist
                    .ToListAsync();

                if (facilities == null || !facilities.Any())
                {
                    return ApiResponseDto<object>.ErrorResponse("Facilities not found", StatusCodes.Status404NotFound);
                }

                return ApiResponseDto<object>.SuccessResponse("Success", StatusCodes.Status200OK, facilities);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        private async Task<JwtPayload>? DecodeJwtPayload(string jwt)
        {
            try
            {
                var payload = jwt.Split('.')[1];
                var padded = payload.Length % 4 == 0 ? payload : payload + new string('=', 4 - payload.Length % 4);
                var bytes = Convert.FromBase64String(padded);
                var json = Encoding.UTF8.GetString(bytes);
                return JsonConvert.DeserializeObject<JwtPayload>(json);
            }
            catch
            {
                return null;
            }
        }
    }
}
