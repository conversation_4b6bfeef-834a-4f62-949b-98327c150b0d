﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstAuditLogs
    {
        [Key]
        public int AuditLogID { get; set; }
        public string TableName { get; set; }
        public int? RecordPrimaryKey { get; set; }
        public string OperationType { get; set; }
        public string OldValues { get; set; }
        public string NewValues { get; set; }
        public int ChangedBy { get; set; }
        public DateTime? ChangedAt { get; set; }
    }
}
